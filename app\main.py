from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from jose import JWTError, jwt
from decouple import config
from .routers import auth, all_reports
from .database.connection import db
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="TherapyPMS Report")

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Templates
templates = Jinja2Templates(directory="app/templates")

# Include routers
app.include_router(auth.router)
app.include_router(all_reports.router)

SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config('ALGORITHM')

def get_current_user(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        return None
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload.get("user_id")
    except JWTError:
        return None

# Middleware to handle authentication like Next.js middleware
@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    # Skip auth for static files and API endpoints
    if request.url.path.startswith("/static") or request.url.path.startswith("/health"):
        return await call_next(request)
    
    # If accessing login page and already authenticated, redirect to dashboard
    if request.url.path.startswith("/login"):
        user_id = get_current_user(request)
        if user_id and request.method == "GET":
            return RedirectResponse(url="/", status_code=302)
        # Allow login page access for POST requests or unauthenticated users
        return await call_next(request)
    
    # For all other pages, check authentication
    user_id = get_current_user(request)
    if not user_id:
        return RedirectResponse(url="/login", status_code=302)
    
    return await call_next(request)

@app.on_event("startup")
async def startup_event():
    try:
        db.connect()
        logger.info("Application started successfully")
    except Exception as e:
        logger.error(f"Startup error: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    db.close()

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    # Authentication handled by middleware
    return templates.TemplateResponse("dashboard.html", {"request": request})

# Reports that need DATE FILTERS (actually use StartEndDate component in Next.js)
date_filter_reports = [
    # Staff reports with date filters
    'expiring_credentials', 'time_of_mgmt', 'provider_missing_sign',
    
    # Patient reports with date filters
    'expired_auth', 'expiring_auth', 'auth_placeholder', 'expiring_doc',
    
    # Appointment reports with date filters  
    'scheduled_not_rendered', 'scheduled_not_attended', 'session_missing_signature',
    'session_note_missing', 'session_unlocked_notes',
    
    # All reports under /reports/ category with date filters
    'schedule_billable', 'schedule_non_billable', 'payment_deposits',
    'kpi_by_month', 'kpi_by_patient', 'kpi_by_insurance',
    'aba_hour_client', 'aba_hour_provider', 'appointment_billed',
    'appointment_ledger', 'ar_ledger_with_balance', 'exprected_actual_pr',
    'max_total_auth_total', 'ratewise_payroll_detail', 'ratewise_payroll_summary',
    'service_payroll_detail', 'service_payroll_summary'
]

# Reports that DON'T need date filters (direct load with simple button)
direct_load_reports = [
    # Staff reports without date filters
    'active_staff', 'allstaffs', 'missing_credentials',
    
    # Patient reports without date filters
    'without_auth', 'non_payor_tag', 'arrived_info'
]

@app.get("/admin/{category}/{report_type}", response_class=HTMLResponse)
async def report_page(request: Request, category: str, report_type: str):
    # Authentication handled by middleware
    
    # Determine template based on whether report needs date filters
    if report_type in date_filter_reports:
        template_name = "reports/with_date_filters.html"
    elif report_type in direct_load_reports:
        template_name = "reports/direct_load.html"
    else:
        # Default to generic template that can handle both
        template_name = "reports/generic_report.html"
    
    return templates.TemplateResponse(template_name, {
        "request": request, 
        "category": category, 
        "report_type": report_type
    })

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "TherapyPMS Report Dashboard is running"}