{% extends "base.html" %}

{% block title %}{{ report_type|title|replace('_', ' ') }} - TherapyPMS Report{% endblock %}

{% block content %}
<div>
    <div class="rounded-md bg-white py-4 px-2 flex">
        <div class="text-md font-semibold p-2 text-tpms">
            <h2 class="border-b-4">{{ report_type|title|replace('_', ' ') }}</h2>
        </div>
        <div class="flex-1">
            <!-- Date filter form (shown for reports that need date filters) -->
            <form id="dateForm" class="space-y-6" style="display: none;">
                <div class="flex mt-1 justify-end">
                    <div class="flex flex-row">
                        <div class="mt-1"><label>Start Date</label></div>
                        <div class="mx-2">
                            <input
                                type="date"
                                id="startDate"
                                name="startDate"
                                class="form-input block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-tpms sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mt-1"><label>End Date</label></div>
                        <div class="mx-2">
                            <input
                                type="date"
                                id="endDate"
                                name="endDate"
                                class="form-input block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-tpms sm:text-sm sm:leading-6"
                            />
                        </div>
                    </div>
                    <div>
                        <button type="submit" class="bg-tpms text-white p-1 rounded-md px-3">View Data</button>
                    </div>
                </div>
            </form>
            
            <!-- Direct load button (shown for reports that don't need date filters) -->
            <div id="directLoadDiv" class="flex mt-1 justify-end">
                <div>
                    <button id="viewDataBtn" class="bg-tpms text-white p-1 rounded-md px-3">View Data</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AG Grid Container -->
    <div id="agGridContainer" style="display: none;">
        <div class="ag-theme-alpine grid-container mt-3">
            <div class="flex mb-2">
                <button id="exportBtn" class="border p-2 bg-white text-gray-900">Download CSV</button>
                <button id="clearFilterBtn" class="border p-2 bg-red-400 mx-2 text-white">Clear Filter</button>
            </div>
            <div id="myGrid" class="ag-grid"></div>
        </div>
    </div>
    
    <!-- Loading indicator -->
    <div id="loadingIndicator" class="hidden text-center py-8">
        <div class="loading"></div>
        <span class="ml-2">Loading data...</span>
    </div>
    
    <!-- Error message -->
    <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4">
        <span id="errorText"></span>
    </div>
</div>

<script>
let gridApi;

// Reports that require date filters (based on actual Next.js StartEndDate usage)
const dateFilterReports = [
    // Staff reports with date filters
    'expiring_credentials', 'time_of_mgmt',
    
    // Patient reports with date filters
    'expired_auth', 'expiring_auth', 'auth_placeholder', 'expiring_doc',
    
    // Appointment reports with date filters  
    'scheduled_not_rendered', 'scheduled_not_attended', 'session_missing_signature',
    'session_note_missing', 'session_unlocked_notes',
    
    // All reports under /reports/ category with date filters
    'schedule_billable', 'schedule_non_billable', 'payment_deposits',
    'kpi_by_month', 'kpi_by_patient', 'kpi_by_insurance',
    'aba_hour_client', 'aba_hour_provider', 'appointment_billed',
    'appointment_ledger', 'ar_ledger_with_balance', 'exprected_actual_pr',
    'max_total_auth_total', 'ratewise_payroll_detail', 'ratewise_payroll_summary',
    'service_payroll_detail', 'service_payroll_summary'
];

const reportType = '{{ report_type }}';
const category = '{{ category }}';
const needsDateFilter = dateFilterReports.includes(reportType);

// Show appropriate form based on report type
if (needsDateFilter) {
    document.getElementById('dateForm').style.display = 'block';
    document.getElementById('directLoadDiv').style.display = 'none';
} else {
    document.getElementById('dateForm').style.display = 'none';
    document.getElementById('directLoadDiv').style.display = 'block';
}

const defaultColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true,
    floatingFilter: true
};

const dataTypeDefinitions = {
    dateString: {
        baseDataType: "dateString",
        extendsDataType: "dateString",
        valueParser: (params) =>
            params.newValue != null &&
            params.newValue.match("\\d{2}/\\d{2}/\\d{4}")
                ? params.newValue
                : null,
        valueFormatter: (params) => (params.value == null ? "" : params.value),
        dataTypeMatcher: (value) =>
            typeof value === "string" && !!value.match("\\d{2}/\\d{2}/\\d{4}"),
        dateParser: (value) => {
            if (value == null || value === "") {
                return undefined;
            }
            const dateParts = value.split("/");
            return dateParts.length === 3
                ? new Date(
                    parseInt(dateParts[2]),
                    parseInt(dateParts[0]) - 1,
                    parseInt(dateParts[1]),
                )
                : undefined;
        },
        dateFormatter: (value) => {
            if (value == null) {
                return undefined;
            }
            const date = String(value.getDate());
            const month = String(value.getMonth() + 1);
            return `${month.length === 1 ? "0" + month : month}/${date.length === 1 ? "0" + date : date}/${value.getFullYear()}`;
        },
    },
};

function showError(message) {
    document.getElementById('errorText').textContent = message;
    document.getElementById('errorMessage').classList.remove('hidden');
}

function hideError() {
    document.getElementById('errorMessage').classList.add('hidden');
}

function showLoading() {
    document.getElementById('loadingIndicator').classList.remove('hidden');
    document.getElementById('agGridContainer').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').classList.add('hidden');
}

const onGridReady = (params) => {
    gridApi = params.api;
};

const onBtnExport = () => {
    gridApi.exportDataAsCsv();
};

const clearFilters = () => {
    gridApi.setFilterModel(null);
};

async function loadData(useFormData = false) {
    hideError();
    showLoading();
    
    try {
        let requestOptions = {
            method: 'POST'
        };
        
        if (useFormData) {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                showError('Please select start date and end date');
                hideLoading();
                return;
            }
            
            const formData = new FormData();
            formData.append('start_date', startDate);
            formData.append('end_date', endDate);
            requestOptions.body = formData;
        } else {
            requestOptions.headers = {
                'Content-Type': 'application/json'
            };
            requestOptions.body = JSON.stringify({ status: '' });
        }
        
        const response = await fetch(`/api/admin/${category}/${reportType}`, requestOptions);
        
        hideLoading();
        
        if (response.ok) {
            const data = await response.json();
            
            if (data && data.length > 0) {
                // Auto-generate column definitions from data
                const columnDefs = Object.keys(data[0]).map(key => ({
                    headerName: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    field: key,
                    filter: key.includes('date') ? "agDateColumnFilter" : 
                           (typeof data[0][key] === 'number' ? "agNumberColumnFilter" : "agTextColumnFilter")
                }));
                
                document.getElementById('agGridContainer').style.display = 'block';
                
                const gridDiv = document.querySelector('#myGrid');
                const gridOptions = {
                    columnDefs: columnDefs,
                    rowData: data,
                    defaultColDef: defaultColDef,
                    dataTypeDefinitions: dataTypeDefinitions,
                    suppressExcelExport: true,
                    onGridReady: onGridReady,
                    pagination: true,
                    paginationPageSize: 10,
                    paginationPageSizeSelector: [10, 25, 50, 100, 1000],
                    suppressAndOrCondition: true
                };
                
                if (gridApi) {
                    gridApi.destroy();
                }
                
                gridApi = agGrid.createGrid(gridDiv, gridOptions);
            } else {
                showError(useFormData ? 'No data found for the selected date range' : 'No data found');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            showError(errorData.detail || 'Something went wrong!');
        }
    } catch (error) {
        hideLoading();
        console.error('Error:', error);
        showError('Something went wrong!');
    }
}

// Event listeners
if (needsDateFilter) {
    document.getElementById('dateForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await loadData(true);
    });
} else {
    document.getElementById('viewDataBtn').addEventListener('click', async () => {
        await loadData(false);
    });
}

document.getElementById('exportBtn').addEventListener('click', onBtnExport);
document.getElementById('clearFilterBtn').addEventListener('click', clearFilters);
</script>
{% endblock %}