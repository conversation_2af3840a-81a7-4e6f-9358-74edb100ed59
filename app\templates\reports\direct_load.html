{% extends "base.html" %}

{% block title %}{{ report_type|title|replace('_', ' ') }} - TherapyPMS Reports{% endblock %}
{% block page_title %}{{ report_type|title|replace('_', ' ') }}{% endblock %}
{% block page_description %}View {{ report_type|replace('_', ' ') }} data instantly{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Action Card -->
    <div class="card p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ report_type|title|replace('_', ' ') }}</h2>
                <p class="text-gray-600">Click the button below to load the latest data</p>
            </div>
            
            <button id="viewDataBtn" class="btn-primary flex items-center space-x-2">
                <iconify-icon icon="lucide:play" class="text-lg"></iconify-icon>
                <span>Load Data</span>
            </button>
        </div>
    </div>
    
    <!-- Loading State -->
    <div id="loadingCard" class="hidden card p-8">
        <div class="flex flex-col items-center justify-center space-y-4">
            <div class="loading"></div>
            <p class="text-gray-600">Loading report data...</p>
        </div>
    </div>
    
    <!-- Error State -->
    <div id="errorCard" class="hidden card p-6 border-l-4 border-red-500 bg-red-50">
        <div class="flex items-start space-x-3">
            <iconify-icon icon="lucide:alert-circle" class="text-red-500 text-xl mt-0.5"></iconify-icon>
            <div>
                <h3 class="text-lg font-medium text-red-800">Error Loading Data</h3>
                <p id="errorText" class="text-red-700 mt-1"></p>
                <button onclick="document.getElementById('viewDataBtn').click()" class="mt-3 text-sm text-red-600 hover:text-red-800 underline">
                    Try Again
                </button>
            </div>
        </div>
    </div>
    
    <!-- Data Grid -->
    <div id="gridCard" class="hidden card p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 space-y-4 lg:space-y-0">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Report Results</h3>
                <p id="recordCount" class="text-gray-600"></p>
            </div>
            <div class="flex flex-wrap gap-3">
                <button id="exportBtn" class="btn-secondary flex items-center space-x-2">
                    <iconify-icon icon="lucide:download" class="text-lg"></iconify-icon>
                    <span>Export CSV</span>
                </button>
                <button id="clearFilterBtn" class="btn-secondary flex items-center space-x-2">
                    <iconify-icon icon="lucide:filter-x" class="text-lg"></iconify-icon>
                    <span>Clear Filters</span>
                </button>
                <button id="refreshBtn" class="btn-secondary flex items-center space-x-2">
                    <iconify-icon icon="lucide:refresh-cw" class="text-lg"></iconify-icon>
                    <span>Refresh</span>
                </button>
            </div>
        </div>
        
        <div class="ag-theme-quartz" style="height: 600px; width: 100%;">
            <div id="myGrid"></div>
        </div>
    </div>
    
    <!-- Empty State -->
    <div id="emptyCard" class="hidden card p-12">
        <div class="text-center">
            <iconify-icon icon="lucide:inbox" class="text-6xl text-gray-300 mb-4"></iconify-icon>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
            <p class="text-gray-600 mb-6">There are currently no records to display for this report.</p>
            <button onclick="document.getElementById('viewDataBtn').click()" class="btn-primary">
                Reload Data
            </button>
        </div>
    </div>
</div>

<script>
let gridApi;

const defaultColDef = {
    flex: 1,
    minWidth: 120,
    resizable: true,
    sortable: true,
    filter: true,
    floatingFilter: true,
    cellStyle: { padding: '12px' }
};

const dataTypeDefinitions = {
    dateString: {
        baseDataType: "dateString",
        extendsDataType: "dateString",
        valueParser: (params) =>
            params.newValue != null &&
            params.newValue.match("\\d{2}/\\d{2}/\\d{4}")
                ? params.newValue
                : null,
        valueFormatter: (params) => (params.value == null ? "" : params.value),
        dataTypeMatcher: (value) =>
            typeof value === "string" && !!value.match("\\d{2}/\\d{2}/\\d{4}"),
        dateParser: (value) => {
            if (value == null || value === "") return undefined;
            const dateParts = value.split("/");
            return dateParts.length === 3
                ? new Date(parseInt(dateParts[2]), parseInt(dateParts[0]) - 1, parseInt(dateParts[1]))
                : undefined;
        },
        dateFormatter: (value) => {
            if (value == null) return undefined;
            const date = String(value.getDate());
            const month = String(value.getMonth() + 1);
            return `${month.length === 1 ? "0" + month : month}/${date.length === 1 ? "0" + date : date}/${value.getFullYear()}`;
        },
    },
};

function showCard(cardId) {
    ['loadingCard', 'errorCard', 'gridCard', 'emptyCard'].forEach(id => {
        document.getElementById(id).classList.add('hidden');
    });
    document.getElementById(cardId).classList.remove('hidden');
}

function showError(message) {
    document.getElementById('errorText').textContent = message;
    showCard('errorCard');
}

const onGridReady = (params) => {
    gridApi = params.api;
};

const onBtnExport = () => {
    gridApi.exportDataAsCsv({
        fileName: '{{ report_type }}_' + new Date().toISOString().split('T')[0] + '.csv'
    });
};

const clearFilters = () => {
    gridApi.setFilterModel(null);
};

const refreshData = () => {
    document.getElementById('viewDataBtn').click();
};

document.getElementById('viewDataBtn').addEventListener('click', async () => {
    showCard('loadingCard');
    
    try {
        const response = await fetch('/api/admin/{{ category }}/{{ report_type }}', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: '' })
        });
        
        if (response.ok) {
            const data = await response.json();
            
            if (data && data.length > 0 && data[0].error) {
                showError(data[0].error + ': ' + data[0].message);
                return;
            }
            
            if (data && data.length > 0) {
                // Update record count
                document.getElementById('recordCount').textContent = `${data.length} records found`;
                
                // Generate column definitions
                const columnDefs = Object.keys(data[0]).map(key => ({
                    headerName: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    field: key,
                    filter: key.includes('date') ? "agDateColumnFilter" : 
                           (typeof data[0][key] === 'number' ? "agNumberColumnFilter" : "agTextColumnFilter"),
                    cellRenderer: key.includes('date') ? null : 
                                 key.includes('status') ? (params) => {
                                     const status = params.value;
                                     const colors = {
                                         'Active': 'bg-green-100 text-green-800',
                                         'Expired': 'bg-red-100 text-red-800',
                                         'Pending': 'bg-yellow-100 text-yellow-800',
                                         'Completed': 'bg-blue-100 text-blue-800',
                                         'Missing': 'bg-orange-100 text-orange-800'
                                     };
                                     const colorClass = colors[status] || 'bg-gray-100 text-gray-800';
                                     return `<span class="px-2 py-1 rounded-full text-xs font-medium ${colorClass}">${status}</span>`;
                                 } : null
                }));
                
                const gridOptions = {
                    columnDefs: columnDefs,
                    rowData: data,
                    defaultColDef: defaultColDef,
                    dataTypeDefinitions: dataTypeDefinitions,
                    onGridReady: onGridReady,
                    pagination: true,
                    paginationPageSize: 25,
                    paginationPageSizeSelector: [10, 25, 50, 100],
                    suppressExcelExport: true,
                    animateRows: true,
                    enableRangeSelection: true,
                    suppressAndOrCondition: true
                };
                
                if (gridApi) gridApi.destroy();
                gridApi = agGrid.createGrid(document.getElementById('myGrid'), gridOptions);
                
                showCard('gridCard');
            } else {
                showCard('emptyCard');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            showError(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        showError('Network error: ' + error.message);
    }
});

// Event listeners
document.getElementById('exportBtn').addEventListener('click', onBtnExport);
document.getElementById('clearFilterBtn').addEventListener('click', clearFilters);
document.getElementById('refreshBtn').addEventListener('click', refreshData);
</script>
{% endblock %}