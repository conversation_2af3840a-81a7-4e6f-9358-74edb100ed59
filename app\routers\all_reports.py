from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import JSONResponse
from jose import JWTError, jwt
from decouple import config
from datetime import datetime, timedelta
from ..database.connection import db
from typing import Optional
import logging
import json
from pydantic import BaseModel

class DateRangeRequest(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None

logger = logging.getLogger(__name__)
router = APIRouter()

SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config('ALGORITHM')

def get_current_user(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_admin_info(user_id: int):
    try:
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT is_up_admin, up_admin_id FROM admins WHERE id = %s", (user_id,))
        user_info = cursor.fetchone()
        if not user_info:
            return user_id
        admin_id = user_id if user_info.get('is_up_admin') == 1 else user_info.get('up_admin_id', user_id)
        return admin_id
    except Exception as e:
        logger.error(f"Error getting admin info: {e}")
        return user_id

def serialize_datetime(obj):
    """Convert datetime objects to string for JSON serialization"""
    if isinstance(obj, datetime):
        return obj.strftime('%m/%d/%Y')
    return obj

def convert_row_to_dict(row):
    """Convert database row to dictionary with datetime serialization"""
    result = {}
    for key, value in row.items():
        result[key] = serialize_datetime(value)
    return result

def get_client_name_query(admin_id: int):
    if admin_id == 35:
        return 'c.client_full_name'
    else:
        return """(CASE
                    WHEN (c.client_last_name IS NOT NULL) AND (c.client_first_name IS NOT NULL)
                    THEN CONCAT(c.client_last_name,', ',c.client_first_name)
                    ELSE c.client_full_name
                  END)"""

def get_staff_name_query(admin_id: int):
    if admin_id == 35:
        return 'e.full_name'
    else:
        return """(CASE
                    WHEN (e.last_name IS NOT NULL) AND (e.first_name IS NOT NULL)
                    THEN CONCAT(e.last_name,', ',e.first_name)
                    ELSE e.full_name
                  END)"""

# Staff Reports
@router.post("/api/admin/staffs/active_staff")
async def get_active_staff(request: Request, user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        query = f"""
            SELECT e.last_name, e.first_name, e.office_phone, 
            to_char(cast(e.hir_date_compnay as date), 'MM/DD/YYYY') as hir_date_compnay, 
            e.title, e.office_email, eta.type_name,
            to_char(cast(e.staff_birthday as date), 'MM/DD/YYYY') as staff_birthday, 
            e.individual_npi, e.language, e.id as staffid, 'Yes' as active, e.notes
            FROM employees as e
            LEFT JOIN employee_type_assigns as eta on eta.id=e.credential_type
            WHERE e.admin_id={admin_id} AND e.is_active = 1
            ORDER BY e.last_name, e.first_name
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in active_staff: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/admin/staffs/missing_credentials")
async def get_missing_credentials(request: Request, user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        query = f"""
            SELECT last_name, first_name 
            FROM employees 
            WHERE admin_id = {admin_id} 
            AND NOT EXISTS (
                SELECT * FROM employee_credentials 
                WHERE employees.id = employee_credentials.employee_id
            ) 
            AND is_active = 1 
            AND is_deleted IS NULL 
            ORDER BY LOWER(last_name) ASC, LOWER(first_name) ASC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in missing_credentials: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/admin/staffs/expiring_credentials")
async def get_expiring_credentials(request: Request, 
                                 date_request: DateRangeRequest,
                                 user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        
        if not start_date or not end_date:
            raise HTTPException(status_code=400, detail="Start date and end date are required")
        
        staff_name_query = get_staff_name_query(admin_id)
        
        # Try each query separately to identify issues
        try:
            query1 = f"""
                SELECT ec.id, {staff_name_query}, 'Credentials' as document_type, 1 as doucument_id, 
                 ec.credential_name as description,
                 to_char(cast(ec.credential_date_issue as date), 'MM/DD/YYYY') as date_issue,
                 to_char(cast(ec.credential_date_expired as date), 'MM/DD/YYYY') as date_expired,
                 CASE WHEN ec.credential_file is null THEN 'No File' ELSE '' END as credential_file
                 FROM employees as e
                 LEFT JOIN employee_credentials as ec on ec.employee_id=e.id
                 WHERE e.admin_id={admin_id}
                 AND ec.credential_date_expired between '{start_date}' and '{end_date}'
                 AND ec.credential_date_expired IS NOT NULL
            """
            cursor.execute(query1)
            results1 = cursor.fetchall()
        except Exception as e1:
            logger.error(f"Error in credentials query: {e1}")
            results1 = []
        
        try:
            query2 = f"""
                SELECT ec.id, {staff_name_query}, 'Clearance' as document_type, 2 as doucument_id, 
                 ec.clearance_name as description,
                 to_char(cast(ec.clearance_date_issue as date), 'MM/DD/YYYY') as date_issue,
                 to_char(cast(ec.clearance_date_exp as date), 'MM/DD/YYYY') as date_expired,
                 CASE WHEN ec.clearance_file is null THEN 'No File' ELSE '' END as credential_file
                 FROM employees as e
                 LEFT JOIN employee_clearances as ec on ec.employee_id=e.id
                 WHERE e.admin_id={admin_id}
                 AND ec.clearance_date_exp between '{start_date}' and '{end_date}'
                 AND ec.clearance_date_exp IS NOT NULL
            """
            cursor.execute(query2)
            results2 = cursor.fetchall()
        except Exception as e2:
            logger.error(f"Error in clearances query: {e2}")
            results2 = []
        
        try:
            query3 = f"""
                SELECT ec.id, {staff_name_query}, 'Qualification' as document_type, 3 as doucument_id, 
                 ec.qualification_name as description,
                 to_char(cast(ec.qualification_date_issue as date), 'MM/DD/YYYY') as date_issue,
                 to_char(cast(ec.qualification_date_exp as date), 'MM/DD/YYYY') as date_expired,
                 CASE WHEN ec.qualification_file is null THEN 'No File' ELSE '' END as credential_file
                 FROM employees as e
                 LEFT JOIN employee_qualifications as ec on ec.employee_id=e.id
                 WHERE e.admin_id={admin_id}
                 AND ec.qualification_date_exp between '{start_date}' and '{end_date}'
                 AND ec.qualification_date_exp IS NOT NULL
            """
            cursor.execute(query3)
            results3 = cursor.fetchall()
        except Exception as e3:
            logger.error(f"Error in qualifications query: {e3}")
            results3 = []
        
        # Combine results
        all_results = []
        if results1:
            all_results.extend([convert_row_to_dict(row) for row in results1])
        if results2:
            all_results.extend([convert_row_to_dict(row) for row in results2])
        if results3:
            all_results.extend([convert_row_to_dict(row) for row in results3])
        
        # Sort by document type
        all_results.sort(key=lambda x: x.get('doucument_id', 0))
        
        return JSONResponse(content=all_results)
    except Exception as e:
        logger.error(f"Error in expiring_credentials: {e}")
        return JSONResponse(content=[{"error": str(e), "message": "Error loading expiring credentials"}], status_code=500)

@router.post("/api/admin/staffs/time_of_mgmt")
async def get_time_of_mgmt(request: Request, 
                         date_request: DateRangeRequest,
                         user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        staff_name_query = get_staff_name_query(admin_id)
        
        query = f"""
            SELECT {staff_name_query},
            to_char(cast(el.leave_date as date), 'MM/DD/YYYY') as leave_date,
            el.description, 
            CASE WHEN el.status is null THEN 'Pending' ELSE initcap(el.status) END as status
            FROM employee_leaves as el
            LEFT JOIN employees as e on e.id=el.employee_id
            WHERE el.admin_id={admin_id} 
            AND el.leave_date between '{start_date}' and '{end_date}'
            ORDER BY el.id desc
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in time_of_mgmt: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/admin/staffs/provider_missing_sign")
async def get_provider_missing_sign(request: Request, 
                                   date_request: DateRangeRequest,
                                   user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        
        if not start_date or not end_date:
            raise HTTPException(status_code=400, detail="Start date and end date are required")
        
        staff_name_query = get_staff_name_query(admin_id)
        client_name_query = get_client_name_query(admin_id)
        
        query = f"""
            SELECT 
            (CASE
                WHEN a.billable = 1
                THEN {client_name_query} 
                ELSE 'Non-Billable Client'
            END) AS client_full_name,
            {staff_name_query} as full_name, 
            a.time_duration,
            concat(to_char(cast(a.from_time as time), 'HH12:MI AM'),' - ',to_char(cast(a.to_time as time), 'HH12:MI AM')) as from_to_time,
            to_char(cast(a.schedule_date as date), 'MM/DD/YYYY') as dos, 
            a.status,
            COALESCE((a.time_duration / 60.0), 0) as hours
            FROM appoinments as a
            LEFT JOIN clients as c on c.id=a.client_id
            LEFT JOIN employees as e on e.id=a.provider_id
            WHERE a.admin_id = {admin_id} 
            AND NOT EXISTS (
                SELECT * FROM appoinment_signatures 
                WHERE a.id = appoinment_signatures.session_id 
                AND user_type = 2
            ) 
            AND NOT EXISTS (
                SELECT * FROM api_session_files 
                WHERE a.id = api_session_files.session_id
            ) 
            AND a.schedule_date between '{start_date}' and '{end_date}' 
            AND a.status != 'deleted'
            ORDER BY a.schedule_date DESC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        # Process results to match Next.js format
        processed_results = []
        for row in results:
            row_dict = convert_row_to_dict(row)
            hours = row_dict.get('hours', 0)
            service = f"Service ({hours} Hrs)"  # Simplified since we don't have getServiceName function
            
            processed_row = {
                'client_full_name': row_dict['client_full_name'],
                'dos': row_dict['dos'],
                'from_to_time': row_dict['from_to_time'],
                'full_name': row_dict['full_name'],
                'service': service,
                'status': row_dict['status']
            }
            processed_results.append(processed_row)
        
        return JSONResponse(content=processed_results)
    except Exception as e:
        logger.error(f"Error in provider_missing_sign: {e}")
        return JSONResponse(content=[{"error": str(e), "message": "Error loading provider missing signatures"}], status_code=500)

# Patient Reports
@router.post("/api/admin/patients/expiring_doc")
async def get_expiring_doc(request: Request, 
                         date_request: DateRangeRequest,
                         user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        client_name_query = get_client_name_query(admin_id)
        
        query = f"""
            SELECT {client_name_query} as client_full_name,
            REPLACE(cd.file_name, 'assets/dashboard/documents/', '') as file_name,
            to_char(cast(cd.exp_date as date), 'MM/DD/YYYY') as exp_date
            FROM client_documents as cd
            LEFT JOIN clients as c on c.id=cd.client_id
            WHERE cd.admin_id={admin_id}
            AND cd.exp_date <= '{end_date}' AND cd.exp_date >= '{start_date}'
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in expiring_doc: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/admin/patients/without_auth")
async def get_without_auth(request: Request, user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        query = f"""
            SELECT client_last_name, client_first_name 
            FROM clients 
            WHERE admin_id = {admin_id} 
            AND EXISTS (
                SELECT * FROM "client_statuses" 
                WHERE "clients"."is_active_client" = "client_statuses"."id" 
                AND "is_active" = 1
            ) 
            AND NOT EXISTS (
                SELECT * FROM "client_authorizations" 
                WHERE "clients"."id" = "client_authorizations"."client_id" 
                AND "is_deleted" IS NULL
            ) 
            AND "is_deleted" IS NULL 
            ORDER BY LOWER(client_last_name) ASC, LOWER(client_first_name) ASC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in without_auth: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/admin/patients/expired_auth")
async def get_expired_auth(request: Request, 
                          date_request: DateRangeRequest,
                          user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        client_name_query = get_client_name_query(admin_id)
        staff_name_query = get_staff_name_query(admin_id)
        
        query = f"""
            WITH PatientStatus AS (SELECT id, admin_id FROM client_statuses WHERE is_active=1)
            SELECT ca.id, ca.client_id, ca.payor_id, ca.end_date, ca.authorization_number, 
            ca.supervisor_id, ap.payor_name,
            {client_name_query} as client_fullname, c.is_active_client, 
            {staff_name_query} as supervisor_name,
            to_char(cast(ca.end_date as date), 'MM/DD/YYYY') as end_date, 
            ca.authorization_number,
            (CASE
                WHEN (PatientStatus.id IS NOT NULL) OR (c.is_active_client = 1)
                THEN 'Active'
                ELSE 'In-Active'
            END) as patient_status
            FROM client_authorizations as ca 
            LEFT JOIN clients as c on c.id=ca.client_id
            LEFT JOIN PatientStatus on PatientStatus.id = c.is_active_client and PatientStatus.admin_id = {admin_id} 
            LEFT JOIN employees as e on e.id=ca.supervisor_id
            LEFT JOIN all_payors as ap on ap.id = ca.payor_id
            WHERE ca.admin_id = {admin_id} 
            AND ca.end_date between '{start_date}' and '{end_date}' 
            AND ca.is_valid = 1 AND ca.is_deleted is null 
            ORDER BY ca.end_date desc
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in expired_auth: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Appointment Reports
@router.post("/api/admin/appointments/scheduled_not_rendered")
async def get_scheduled_not_rendered(request: Request, 
                                   date_request: DateRangeRequest,
                                   user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        today_date = datetime.now().strftime('%Y-%m-%d')
        client_name_query = get_client_name_query(admin_id)
        staff_name_query = get_staff_name_query(admin_id)
        
        query = f"""
            SELECT 
            (CASE
                WHEN ca.billable = 1
                THEN {client_name_query} 
                ELSE 'Non-Billable Client'
            END) AS client_full_name, 
            {staff_name_query} as full_name,
            to_char(cast(ca.schedule_date as date), 'MM/DD/YYYY') as schedule_date, 
            ca.status,
            concat(to_char(cast(ca.from_time as time), 'HH12:MI AM'),' - ',to_char(cast(ca.to_time as time), 'HH12:MI AM')) as from_to_time,
            (CASE
                WHEN ca.billable = 1
                THEN 'Billable'
                ELSE 'Non-Billable'
            END) AS is_billable
            FROM appoinments as ca
            LEFT JOIN clients as c on c.id=ca.client_id
            LEFT JOIN employees as e on e.id=ca.provider_id
            WHERE ca.admin_id={admin_id} 
            AND status = 'Scheduled'
            AND (ca.is_break is null or ca.is_break=0) 
            AND ca.schedule_date <= '{today_date}'
            AND ca.schedule_date between '{start_date}' and '{end_date}'
            ORDER BY ca.schedule_date desc
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in scheduled_not_rendered: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Additional missing endpoints
@router.post("/api/admin/patients/expiring_auth")
async def get_expiring_auth(request: Request, 
                           date_request: DateRangeRequest,
                           user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        
        if not start_date or not end_date:
            raise HTTPException(status_code=400, detail="Start date and end date are required")
        
        client_name_query = get_client_name_query(admin_id)
        
        query = f"""
            SELECT ca.id, ca.client_id, {client_name_query} as client_fullname,
            ap.payor_name, ca.authorization_number,
            to_char(cast(ca.end_date as date), 'MM/DD/YYYY') as end_date,
            'Expiring Soon' as status
            FROM client_authorizations as ca
            LEFT JOIN clients as c ON c.id = ca.client_id
            LEFT JOIN all_payors as ap ON ap.id = ca.payor_id
            WHERE ca.admin_id = {admin_id} 
            AND ca.end_date between '{start_date}' and '{end_date}'
            AND ca.is_valid = 1 AND (ca.is_deleted IS NULL OR ca.is_deleted = 0)
            ORDER BY ca.end_date ASC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in expiring_auth: {e}")
        return JSONResponse(content=[{"error": str(e), "message": "Error loading expiring authorizations"}], status_code=500)

@router.post("/api/admin/patients/auth_placeholder")
async def get_auth_placeholder(request: Request, 
                              date_request: DateRangeRequest,
                              user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        start_date = date_request.start_date
        end_date = date_request.end_date
        
        if not start_date or not end_date:
            raise HTTPException(status_code=400, detail="Start date and end date are required")
        
        client_name_query = get_client_name_query(admin_id)
        
        query = f"""
            SELECT ca.id, ca.client_id, {client_name_query} as client_fullname,
            ap.payor_name, ca.authorization_number,
            to_char(cast(ca.start_date as date), 'MM/DD/YYYY') as start_date,
            to_char(cast(ca.end_date as date), 'MM/DD/YYYY') as end_date,
            'Placeholder' as status
            FROM client_authorizations as ca
            LEFT JOIN clients as c ON c.id = ca.client_id
            LEFT JOIN all_payors as ap ON ap.id = ca.payor_id
            WHERE ca.admin_id = {admin_id} 
            AND ca.start_date between '{start_date}' and '{end_date}'
            AND ca.is_placeholder = 1
            ORDER BY ca.start_date DESC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in auth_placeholder: {e}")
        return JSONResponse(content=[{"error": str(e), "message": "Error loading authorization placeholders"}], status_code=500)

@router.post("/api/admin/patients/non_payor_tag")
async def get_non_payor_tag(request: Request, user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        client_name_query = get_client_name_query(admin_id)
        
        query = f"""
            SELECT c.id as client_id, {client_name_query} as client_fullname,
            c.client_first_name, c.client_last_name, 
            'Patient/Guarantor Pay' as payor_type
            FROM clients as c
            WHERE c.admin_id = {admin_id} 
            AND c.is_active_client = 1
            AND (c.payor_tag = 'Patient Pay' OR c.payor_tag = 'Guarantor Pay')
            ORDER BY c.client_last_name, c.client_first_name
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in non_payor_tag: {e}")
        return JSONResponse(content=[{"error": str(e), "message": "Error loading non payor tag clients"}], status_code=500)

@router.post("/api/admin/patients/arrived_info")
async def get_arrived_info(request: Request, user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        use_thera = admin_id == 35
        conn = db.get_connection(use_thera=use_thera)
        cursor = conn.cursor()
        
        client_name_query = get_client_name_query(admin_id)
        
        query = f"""
            SELECT {client_name_query} as client_name,
            to_char(cast(a.schedule_date as date), 'MM/DD/YYYY') as appointment_date,
            a.from_time as start_time,
            to_char(cast(a.arrived_time as timestamp), 'MM/DD/YYYY HH24:MI') as arrived_time,
            'Arrived' as status
            FROM appoinments as a
            LEFT JOIN clients as c ON c.id = a.client_id
            WHERE a.admin_id = {admin_id} 
            AND a.arrived_time IS NOT NULL
            AND a.schedule_date >= CURRENT_DATE - INTERVAL '7 days'
            ORDER BY a.schedule_date DESC, a.arrived_time DESC
            LIMIT 100
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        return JSONResponse(content=[convert_row_to_dict(row) for row in results])
    except Exception as e:
        logger.error(f"Error in arrived_info: {e}")
        return JSONResponse(content=[{"error": str(e), "message": "Error loading arrived info"}], status_code=500)

# Generic endpoint for other reports that need basic implementation
@router.post("/api/admin/{category}/{report_type}")
async def get_generic_report(request: Request, category: str, report_type: str, 
                           user_id: int = Depends(get_current_user)):
    try:
        admin_id = get_admin_info(user_id)
        
        # Return placeholder data for reports not yet implemented
        placeholder_data = [{
            "message": f"Report '{report_type}' is available",
            "status": "Under Development",
            "admin_id": admin_id,
            "note": "This report will be implemented with actual database queries"
        }]
        
        return JSONResponse(content=placeholder_data)
    except Exception as e:
        logger.error(f"Error in generic report {category}/{report_type}: {e}")
        return JSONResponse(content=[{"error": str(e), "message": f"Error loading {report_type}"}], status_code=500)