{% extends "base.html" %}

{% block title %}{{ report_type|title|replace('_', ' ') }} Report{% endblock %}

{% block content %}
<div class="mb-6">
    <h2 class="text-2xl font-semibold text-gray-900">{{ report_type|title|replace('_', ' ') }} Report</h2>
</div>

<div class="bg-white rounded-lg shadow p-6">
    <!-- Date Filters -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
        <form id="filterForm" class="flex items-center space-x-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" id="startDate" name="start_date" 
                       class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" id="endDate" name="end_date" 
                       class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="pt-6">
                <button type="button" id="loadData" 
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Load Data
                </button>
            </div>
        </form>
    </div>
    
    <!-- Action Buttons -->
    <div class="mb-4">
        <button id="exportCsv" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 ml-2" style="display:none;">
            Export CSV
        </button>
        <button id="clearFilter" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 ml-2" style="display:none;">
            Clear Filter
        </button>
    </div>
    
    <!-- Loading Indicator -->
    <div id="loading" class="hidden text-center py-4">
        <div class="loading"></div>
        <span class="ml-2">Loading data...</span>
    </div>
    
    <!-- Error Message -->
    <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <span id="errorText"></span>
    </div>
    
    <!-- Data Grid -->
    <div id="myGrid" class="ag-theme-alpine" style="height: 600px; width: 100%; display: none;"></div>
    
    <!-- No Data Message -->
    <div id="noData" class="hidden text-center py-8 text-gray-500">
        No data available for the selected criteria.
    </div>
</div>

<script>
let gridApi;

const gridOptions = {
    defaultColDef: {
        flex: 1,
        minWidth: 100,
        resizable: true,
        sortable: true,
        filter: true
    },
    pagination: true,
    paginationPageSize: 25,
    paginationPageSizeSelector: [10, 25, 50, 100, 500]
};

function showError(message) {
    document.getElementById('errorText').textContent = message;
    document.getElementById('errorMessage').classList.remove('hidden');
    document.getElementById('loading').classList.add('hidden');
}

function hideError() {
    document.getElementById('errorMessage').classList.add('hidden');
}

// Set default dates (last 30 days)
document.addEventListener('DOMContentLoaded', function() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
});

document.getElementById('loadData').addEventListener('click', async () => {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!startDate || !endDate) {
        showError('Please select both start and end dates');
        return;
    }
    
    hideError();
    document.getElementById('loading').classList.remove('hidden');
    document.getElementById('myGrid').style.display = 'none';
    document.getElementById('noData').classList.add('hidden');
    
    try {
        const formData = new FormData();
        formData.append('start_date', startDate);
        formData.append('end_date', endDate);
        
        const response = await fetch('/api/admin/{{ category }}/{{ report_type }}', {
            method: 'POST',
            body: formData
        });
        
        document.getElementById('loading').classList.add('hidden');
        
        if (response.ok) {
            const data = await response.json();
            
            if (data && data.length > 0) {
                // Auto-generate column definitions from data
                const columnDefs = Object.keys(data[0]).map(key => ({
                    field: key,
                    headerName: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    filter: true,
                    floatingFilter: true
                }));
                
                gridOptions.columnDefs = columnDefs;
                
                const gridDiv = document.querySelector('#myGrid');
                gridDiv.style.display = 'block';
                
                // Destroy existing grid if it exists
                if (gridApi) {
                    gridApi.destroy();
                }
                
                gridApi = agGrid.createGrid(gridDiv, gridOptions);
                gridApi.setGridOption('rowData', data);
                
                document.getElementById('exportCsv').style.display = 'inline-block';
                document.getElementById('clearFilter').style.display = 'inline-block';
            } else {
                document.getElementById('noData').classList.remove('hidden');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            showError(errorData.detail || 'Failed to load data. Please try again.');
        }
    } catch (error) {
        document.getElementById('loading').classList.add('hidden');
        console.error('Error loading data:', error);
        showError('Network error. Please check your connection and try again.');
    }
});

document.getElementById('exportCsv').addEventListener('click', () => {
    if (gridApi) {
        gridApi.exportDataAsCsv({
            fileName: '{{ report_type }}_report_' + new Date().toISOString().split('T')[0] + '.csv'
        });
    }
});

document.getElementById('clearFilter').addEventListener('click', () => {
    if (gridApi) {
        gridApi.setFilterModel(null);
    }
});
</script>
{% endblock %}