import psycopg2
from psycopg2.extras import RealDictCursor
from decouple import config

def test_connection():
    try:
        connection = psycopg2.connect(
            host=config('DB_HOST'),
            database=config('DB_NAME'),
            user=config('DB_USERNAME'),
            password=config('DB_PASSWORD'),
            port=config('DB_PORT'),
            cursor_factory=RealDictCursor
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM admins")
        result = cursor.fetchone()
        print(f"Database connection successful! Found {result['count']} admins")
        
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' LIMIT 10")
        tables = cursor.fetchall()
        print("Available tables:")
        for table in tables:
            print(f"  - {table['table_name']}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False

if __name__ == "__main__":
    test_connection()