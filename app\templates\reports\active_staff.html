{% extends "base.html" %}

{% block title %}Active Staff Details - TherapyPMS Report{% endblock %}

{% block content %}
<div>
    <div class="rounded-md bg-white py-4 px-2 flex">
        <div class="text-md font-semibold p-2 text-tpms">
            <h2 class="border-b-4">Active Staff Details</h2>
        </div>
        <div class="flex-1">
            <div class="flex mt-1 justify-end">
                <div>
                    <button id="viewDataBtn" class="bg-tpms text-white p-1 rounded-md px-3">View Data</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AG Grid Container -->
    <div id="agGridContainer" style="display: none;">
        <div class="ag-theme-alpine grid-container mt-3">
            <div class="flex mb-2">
                <button id="exportBtn" class="border p-2 bg-white text-gray-900">Download CSV</button>
                <button id="clearFilterBtn" class="border p-2 bg-red-400 mx-2 text-white">Clear Filter</button>
            </div>
            <div id="myGrid" class="ag-grid"></div>
        </div>
    </div>
    
    <!-- Loading indicator -->
    <div id="loadingIndicator" class="hidden text-center py-8">
        <div class="loading"></div>
        <span class="ml-2">Loading data...</span>
    </div>
    
    <!-- Error message -->
    <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4">
        <span id="errorText"></span>
    </div>
</div>

<script>
let gridApi;
let gridColumnApi;

const columnDefs = [
    { headerName: 'Lastname', field: "last_name", filter: "agTextColumnFilter" },
    { headerName: 'Firstname', field: "first_name", filter: "agTextColumnFilter" },
    { headerName: 'Phone Office', field: "office_phone", filter: "agTextColumnFilter" },
    { headerName: 'Hire Date', field: "hir_date_compnay", filter: "agDateColumnFilter"},
    { headerName: 'Title', field: "title", filter: "agTextColumnFilter" },
    { headerName: 'Termination Date', field: "created_at", filter: "agDateColumnFilter" },
    { headerName: 'Email', field: "office_email", filter: "agTextColumnFilter" },
    { headerName: 'Stafftype', field: "type_name", filter: "agTextColumnFilter" },
    { headerName: 'Dob', field: "staff_birthday", filter: "agDateColumnFilter" },
    { headerName: 'Npi', field: "individual_npi", filter: "agTextColumnFilter" },
    { headerName: 'Payroll Partner id', field: "payroll_parter_id", filter: "agTextColumnFilter" },
    { headerName: 'Highest Degree', field: "degree_level", filter: "agTextColumnFilter" },
    { headerName: 'Phonecell', field: "phone_cell", filter: "agTextColumnFilter" },
    { headerName: 'Address 1', field: "address_one", filter: "agTextColumnFilter" },
    { headerName: 'Address 2', field: "address_two", filter: "agTextColumnFilter" },
    { headerName: 'City', field: "city", filter: "agTextColumnFilter" },
    { headerName: 'Statename', field: "state", filter: "agTextColumnFilter" },
    { headerName: 'Zipcode', field: "zip", filter: "agTextColumnFilter" },
    { headerName: 'Phone', field: "main_phone", filter: "agTextColumnFilter" },
    { headerName: 'Language Name', field: "language", filter: "agTextColumnFilter" },
    { headerName: 'Staffid', field: "staffid", filter: "agTextColumnFilter" },
    { headerName: 'Treatment Type', field: "treatement_type", filter: "agTextColumnFilter" },
    { headerName: 'Zone', field: "zone", filter: "agTextColumnFilter" },
    { headerName: 'Service Area Zip', field: "service_area_zip", filter: "agTextColumnFilter" },
    { headerName: 'Driver Licence Number', field: "driver_license", filter: "agTextColumnFilter" },
    { headerName: 'Licence Expiration Date', field: "license_exp_date", filter: "agDateColumnFilter" },
    { headerName: 'Active', field: "active", filter: "agTextColumnFilter" },
    { headerName: 'Notes', field: "notes", filter: "agTextColumnFilter" }
];

const defaultColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true,
    floatingFilter: true
};

const dataTypeDefinitions = {
    dateString: {
        baseDataType: "dateString",
        extendsDataType: "dateString",
        valueParser: (params) =>
            params.newValue != null &&
            params.newValue.match("\\d{2}/\\d{2}/\\d{4}")
                ? params.newValue
                : null,
        valueFormatter: (params) => (params.value == null ? "" : params.value),
        dataTypeMatcher: (value) =>
            typeof value === "string" && !!value.match("\\d{2}/\\d{2}/\\d{4}"),
        dateParser: (value) => {
            if (value == null || value === "") {
                return undefined;
            }
            const dateParts = value.split("/");
            return dateParts.length === 3
                ? new Date(
                    parseInt(dateParts[2]),
                    parseInt(dateParts[0]) - 1,
                    parseInt(dateParts[1]),
                )
                : undefined;
        },
        dateFormatter: (value) => {
            if (value == null) {
                return undefined;
            }
            const date = String(value.getDate());
            const month = String(value.getMonth() + 1);
            return `${month.length === 1 ? "0" + month : month}/${date.length === 1 ? "0" + date : date}/${value.getFullYear()}`;
        },
    },
};

function showError(message) {
    document.getElementById('errorText').textContent = message;
    document.getElementById('errorMessage').classList.remove('hidden');
}

function hideError() {
    document.getElementById('errorMessage').classList.add('hidden');
}

function showLoading() {
    document.getElementById('loadingIndicator').classList.remove('hidden');
    document.getElementById('agGridContainer').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').classList.add('hidden');
}

const onGridReady = (params) => {
    gridApi = params.api;
    gridColumnApi = params.columnApi;
};

const onBtnExport = () => {
    gridApi.exportDataAsCsv();
};

const clearFilters = () => {
    gridApi.setFilterModel(null);
};

document.getElementById('viewDataBtn').addEventListener('click', async () => {
    hideError();
    showLoading();
    
    try {
        const response = await fetch('/api/admin/staffs/active_staff', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: '' })
        });
        
        hideLoading();
        
        if (response.ok) {
            const data = await response.json();
            
            if (data && data.length > 0) {
                document.getElementById('agGridContainer').style.display = 'block';
                
                const gridDiv = document.querySelector('#myGrid');
                const gridOptions = {
                    columnDefs: columnDefs,
                    rowData: data,
                    defaultColDef: defaultColDef,
                    dataTypeDefinitions: dataTypeDefinitions,
                    suppressExcelExport: true,
                    onGridReady: onGridReady,
                    pagination: true,
                    paginationPageSize: 10,
                    paginationPageSizeSelector: [10, 25, 50, 100, 1000],
                    suppressAndOrCondition: true
                };
                
                if (gridApi) {
                    gridApi.destroy();
                }
                
                gridApi = agGrid.createGrid(gridDiv, gridOptions);
            } else {
                showError('No data found');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            showError(errorData.detail || 'Something went wrong!');
        }
    } catch (error) {
        hideLoading();
        console.error('Error:', error);
        showError('Something went wrong!');
    }
});

document.getElementById('exportBtn').addEventListener('click', onBtnExport);
document.getElementById('clearFilterBtn').addEventListener('click', clearFilters);
</script>
{% endblock %}