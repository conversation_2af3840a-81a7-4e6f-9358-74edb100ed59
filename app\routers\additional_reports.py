from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import JSONResponse
from jose import JWTError, jwt
from decouple import config
from datetime import datetime, timedelta
from ..database.connection import db
from typing import Optional

router = APIRouter()

SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config('ALGORITHM')

def get_current_user(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_admin_info(user_id: int):
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT is_up_admin, up_admin_id FROM admins WHERE id = %s", (user_id,))
    user_info = cursor.fetchone()
    admin_id = user_id if user_info['is_up_admin'] == 1 else user_info['up_admin_id']
    return admin_id

# Additional Staff Reports
@router.post("/api/admin/staffs/expiring_credentials")
async def get_expiring_credentials(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    query = """
        SELECT e.first_name, e.last_name, e.office_email, 
        to_char(cast(e.license_exp_date as date), 'MM/DD/YYYY') as license_exp_date,
        e.individual_npi, e.title, 
        CASE 
            WHEN e.license_exp_date <= CURRENT_DATE + INTERVAL '30 days' 
            THEN 'Expiring Soon' 
            ELSE 'Valid' 
        END as status
        FROM employees as e
        WHERE e.admin_id = %s AND e.is_active = 1 
        AND e.license_exp_date IS NOT NULL
        AND e.license_exp_date <= CURRENT_DATE + INTERVAL '90 days'
        ORDER BY e.license_exp_date ASC
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

@router.post("/api/admin/staffs/time_of_mgmt")
async def get_time_off_management(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    query = """
        SELECT e.first_name, e.last_name, e.office_email,
        to_char(cast(tor.start_date as date), 'MM/DD/YYYY') as start_date,
        to_char(cast(tor.end_date as date), 'MM/DD/YYYY') as end_date,
        tor.reason, tor.status, tor.total_days
        FROM time_off_requests as tor
        LEFT JOIN employees as e ON e.id = tor.employee_id
        WHERE tor.admin_id = %s 
        AND tor.start_date >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY tor.start_date DESC
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Additional Patient Reports
@router.post("/api/admin/patients/expiring_auth")
async def get_expiring_auth(request: Request, 
                          start_date: Optional[str] = Form(None),
                          end_date: Optional[str] = Form(None),
                          user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = datetime.now().strftime('%Y-%m-%d')
    if not end_date:
        end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    client_name_query = 'c.client_full_name' if admin_id == 35 else """
        (CASE
            WHEN (c.client_last_name IS NOT NULL) AND (c.client_first_name IS NOT NULL)
            THEN CONCAT(c.client_last_name,', ',c.client_first_name)
            ELSE c.client_full_name
        END)"""
    
    query = f"""
        SELECT ca.id, ca.client_id, {client_name_query} as client_fullname,
        ap.payor_name, ca.authorization_number,
        to_char(cast(ca.end_date as date), 'MM/DD/YYYY') as end_date,
        CASE 
            WHEN ca.end_date <= CURRENT_DATE + INTERVAL '7 days' 
            THEN 'Critical' 
            WHEN ca.end_date <= CURRENT_DATE + INTERVAL '30 days' 
            THEN 'Warning'
            ELSE 'Normal' 
        END as urgency_level
        FROM client_authorizations as ca
        LEFT JOIN clients as c ON c.id = ca.client_id
        LEFT JOIN all_payors as ap ON ap.id = ca.payor_id
        WHERE ca.admin_id = %s 
        AND ca.end_date BETWEEN %s AND %s
        AND ca.is_valid = 1 AND ca.is_deleted IS NULL
        ORDER BY ca.end_date ASC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

@router.post("/api/admin/patients/expiring_doc")
async def get_expiring_documents(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    client_name_query = 'c.client_full_name' if admin_id == 35 else """
        (CASE
            WHEN (c.client_last_name IS NOT NULL) AND (c.client_first_name IS NOT NULL)
            THEN CONCAT(c.client_last_name,', ',c.client_first_name)
            ELSE c.client_full_name
        END)"""
    
    query = f"""
        SELECT c.id as client_id, {client_name_query} as client_fullname,
        cd.document_type, cd.document_name,
        to_char(cast(cd.expiry_date as date), 'MM/DD/YYYY') as expiry_date,
        CASE 
            WHEN cd.expiry_date <= CURRENT_DATE 
            THEN 'Expired' 
            WHEN cd.expiry_date <= CURRENT_DATE + INTERVAL '30 days' 
            THEN 'Expiring Soon'
            ELSE 'Valid' 
        END as status
        FROM client_documents as cd
        LEFT JOIN clients as c ON c.id = cd.client_id
        WHERE cd.admin_id = %s 
        AND cd.expiry_date IS NOT NULL
        AND cd.expiry_date <= CURRENT_DATE + INTERVAL '60 days'
        ORDER BY cd.expiry_date ASC
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Additional Appointment Reports
@router.post("/api/admin/appointments/scheduled_not_attended")
async def get_scheduled_not_attended(request: Request, 
                                   start_date: Optional[str] = Form(None),
                                   end_date: Optional[str] = Form(None),
                                   user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = 'c.client_full_name' if admin_id == 35 else """
        (CASE
            WHEN (c.client_last_name IS NOT NULL) AND (c.client_first_name IS NOT NULL)
            THEN CONCAT(c.client_last_name,', ',c.client_first_name)
            ELSE c.client_full_name
        END)"""
    
    staff_name_query = 'e.full_name' if admin_id == 35 else """
        (CASE
            WHEN (e.last_name IS NOT NULL) AND (e.first_name IS NOT NULL)
            THEN CONCAT(e.last_name,', ',e.first_name)
            ELSE e.full_name
        END)"""
    
    query = f"""
        SELECT a.id, {client_name_query} as client_name, 
        {staff_name_query} as provider_name,
        to_char(cast(a.appointment_date as date), 'MM/DD/YYYY') as appointment_date,
        a.start_time, a.end_time, s.service_name, 
        'Not Attended' as attendance_status,
        a.no_show_reason
        FROM appointments as a
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN employees as e ON e.id = a.provider_id
        LEFT JOIN services as s ON s.id = a.service_id
        WHERE a.admin_id = %s 
        AND a.appointment_date BETWEEN %s AND %s
        AND a.attendance_status = 'no_show'
        ORDER BY a.appointment_date DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Additional Financial Reports
@router.post("/api/admin/reports/payment_deposits")
async def get_payment_deposits(request: Request, 
                             start_date: Optional[str] = Form(None),
                             end_date: Optional[str] = Form(None),
                             user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    query = """
        SELECT p.id, ap.payor_name, p.payment_amount, p.payment_method,
        to_char(cast(p.payment_date as date), 'MM/DD/YYYY') as payment_date,
        to_char(cast(p.deposit_date as date), 'MM/DD/YYYY') as deposit_date,
        p.check_number, p.reference_number, p.deposit_status
        FROM payments as p
        LEFT JOIN all_payors as ap ON ap.id = p.payor_id
        WHERE p.admin_id = %s 
        AND p.payment_date BETWEEN %s AND %s
        ORDER BY p.payment_date DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

@router.post("/api/admin/reports/kpi_by_month")
async def get_kpi_by_month(request: Request, 
                         start_date: Optional[str] = Form(None),
                         end_date: Optional[str] = Form(None),
                         user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    query = """
        SELECT 
        DATE_TRUNC('month', a.appointment_date) as month_year,
        COUNT(*) as total_appointments,
        COUNT(CASE WHEN a.is_rendered = 1 THEN 1 END) as rendered_sessions,
        COUNT(CASE WHEN a.attendance_status = 'attended' THEN 1 END) as attended_sessions,
        COUNT(CASE WHEN a.attendance_status = 'no_show' THEN 1 END) as no_show_sessions,
        SUM(a.billable_units) as total_billable_units,
        AVG(a.billable_units) as avg_units_per_session
        FROM appointments as a
        WHERE a.admin_id = %s 
        AND a.appointment_date BETWEEN %s AND %s
        GROUP BY DATE_TRUNC('month', a.appointment_date)
        ORDER BY month_year DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])