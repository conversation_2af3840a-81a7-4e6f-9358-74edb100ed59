<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TherapyPMS Reports{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/ag-grid-community/dist/ag-grid-community.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-grid.css">
    <link rel="stylesheet" href="https://unpkg.com/ag-grid-community/styles/ag-theme-quartz.css">
    <script src="https://code.iconify.design/3/3.1.1/iconify.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        .bg-primary { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); }
        .bg-secondary { background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%); }
        .text-primary { color: #6366f1; }
        .glass { backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.8); border: 1px solid rgba(255, 255, 255, 0.2); }
        .card { background: white; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; }
        .card:hover { transform: translateY(-2px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .btn-primary { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: white; padding: 12px 24px; border-radius: 12px; font-weight: 500; transition: all 0.2s; }
        .btn-primary:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4); }
        .btn-secondary { background: white; color: #374151; border: 1px solid #e5e7eb; padding: 12px 24px; border-radius: 12px; font-weight: 500; transition: all 0.2s; }
        .btn-secondary:hover { background: #f9fafb; border-color: #d1d5db; }
        .sidebar { background: linear-gradient(180deg, #1e293b 0%, #334155 100%); }
        .menu-item { padding: 12px 16px; margin: 4px 0; border-radius: 12px; transition: all 0.2s; cursor: pointer; }
        .menu-item:hover { background: rgba(255, 255, 255, 0.1); }
        .menu-item.active { background: rgba(99, 102, 241, 0.2); color: #6366f1; }
        .submenu-item { padding: 8px 16px; margin: 2px 0; border-radius: 8px; transition: all 0.2s; cursor: pointer; }
        .submenu-item:hover { background: rgba(255, 255, 255, 0.05); transform: translateX(4px); }
        .ag-theme-quartz { --ag-header-height: 48px; --ag-row-height: 44px; --ag-border-radius: 8px; }
        .loading { width: 20px; height: 20px; border: 2px solid #f3f3f3; border-top: 2px solid #6366f1; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        @media (max-width: 768px) {
            .sidebar { transform: translateX(-100%); }
            .sidebar.open { transform: translateX(0); }
            .main-content { margin-left: 0 !important; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Mobile Menu Overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>
    
    <!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 h-full w-80 sidebar z-50 transform transition-transform duration-300">
        <div class="flex flex-col h-full text-white">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-600">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-primary rounded-2xl flex items-center justify-center">
                            <iconify-icon icon="medical-icon:i-health-services" class="text-2xl"></iconify-icon>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold">TherapyPMS</h1>
                            <p class="text-sm text-gray-300">Reports Dashboard</p>
                        </div>
                    </div>
                    <button id="closeSidebar" class="md:hidden p-2 hover:bg-gray-600 rounded-lg">
                        <iconify-icon icon="lucide:x" class="text-xl"></iconify-icon>
                    </button>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="flex-1 overflow-y-auto p-4 space-y-2">
                <!-- Home -->
                <a href="/" class="menu-item flex items-center space-x-3 text-gray-300 hover:text-white">
                    <iconify-icon icon="lucide:home" class="text-xl"></iconify-icon>
                    <span class="font-medium">Dashboard</span>
                </a>
                
                <!-- Staff -->
                <div class="menu-group">
                    <button class="submenu-toggle menu-item flex items-center justify-between w-full text-gray-300 hover:text-white">
                        <div class="flex items-center space-x-3">
                            <iconify-icon icon="fa6-solid:user-doctor" class="text-xl"></iconify-icon>
                            <span class="font-medium">Staff Reports</span>
                        </div>
                        <iconify-icon icon="lucide:chevron-down" class="chevron transition-transform duration-200"></iconify-icon>
                    </button>
                    <div class="submenu hidden ml-6 mt-2 space-y-1">
                        <a href="/admin/staffs/time_of_mgmt" class="submenu-item block text-sm text-gray-400 hover:text-white">Time Off Management</a>
                        <a href="/admin/staffs/missing_credentials" class="submenu-item block text-sm text-gray-400 hover:text-white">Missing Credentials</a>
                        <a href="/admin/staffs/expiring_credentials" class="submenu-item block text-sm text-gray-400 hover:text-white">Expiring Credentials</a>
                        <a href="/admin/staffs/provider_missing_sign" class="submenu-item block text-sm text-gray-400 hover:text-white">Provider Missing Signatures</a>
                    </div>
                </div>
                
                <!-- Patients -->
                <div class="menu-group">
                    <button class="submenu-toggle menu-item flex items-center justify-between w-full text-gray-300 hover:text-white">
                        <div class="flex items-center space-x-3">
                            <iconify-icon icon="mingcute:user-add-fill" class="text-xl"></iconify-icon>
                            <span class="font-medium">Patient Reports</span>
                        </div>
                        <iconify-icon icon="lucide:chevron-down" class="chevron transition-transform duration-200"></iconify-icon>
                    </button>
                    <div class="submenu hidden ml-6 mt-2 space-y-1">
                        <a href="/admin/patients/expiring_doc" class="submenu-item block text-sm text-gray-400 hover:text-white">Expiring Documents</a>
                        <a href="/admin/patients/without_auth" class="submenu-item block text-sm text-gray-400 hover:text-white">Without Authorization</a>
                        <a href="/admin/patients/expired_auth" class="submenu-item block text-sm text-gray-400 hover:text-white">Expired Authorizations</a>
                        <a href="/admin/patients/expiring_auth" class="submenu-item block text-sm text-gray-400 hover:text-white">Expiring Authorizations</a>
                        <a href="/admin/patients/auth_placeholder" class="submenu-item block text-sm text-gray-400 hover:text-white">Authorization Placeholders</a>
                        <a href="/admin/patients/non_payor_tag" class="submenu-item block text-sm text-gray-400 hover:text-white">Patient/Guarantor Pay</a>
                        <a href="/admin/patients/arrived_info" class="submenu-item block text-sm text-gray-400 hover:text-white">Patient Arrivals</a>
                    </div>
                </div>
                
                <!-- Scheduler -->
                <div class="menu-group">
                    <button class="submenu-toggle menu-item flex items-center justify-between w-full text-gray-300 hover:text-white">
                        <div class="flex items-center space-x-3">
                            <iconify-icon icon="ion:calendar-sharp" class="text-xl"></iconify-icon>
                            <span class="font-medium">Scheduler</span>
                        </div>
                        <iconify-icon icon="lucide:chevron-down" class="chevron transition-transform duration-200"></iconify-icon>
                    </button>
                    <div class="submenu hidden ml-6 mt-2 space-y-1">
                        <a href="/admin/appointments/scheduled_not_rendered" class="submenu-item block text-sm text-gray-400 hover:text-white">Scheduled Not Rendered</a>
                        <a href="/admin/appointments/scheduled_not_attended" class="submenu-item block text-sm text-gray-400 hover:text-white">Sessions Not Attended</a>
                        <a href="/admin/appointments/session_missing_signature" class="submenu-item block text-sm text-gray-400 hover:text-white">Missing Signatures</a>
                        <a href="/admin/appointments/session_note_missing" class="submenu-item block text-sm text-gray-400 hover:text-white">Missing Session Notes</a>
                        <a href="/admin/appointments/session_unlocked_notes" class="submenu-item block text-sm text-gray-400 hover:text-white">Unlocked Notes</a>
                    </div>
                </div>
                
                <!-- Analytics -->
                <div class="menu-group">
                    <button class="submenu-toggle menu-item flex items-center justify-between w-full text-gray-300 hover:text-white">
                        <div class="flex items-center space-x-3">
                            <iconify-icon icon="iconoir:reports" class="text-xl"></iconify-icon>
                            <span class="font-medium">Analytics</span>
                        </div>
                        <iconify-icon icon="lucide:chevron-down" class="chevron transition-transform duration-200"></iconify-icon>
                    </button>
                    <div class="submenu hidden ml-6 mt-2 space-y-1">
                        <a href="/admin/reports/schedule_billable" class="submenu-item block text-sm text-gray-400 hover:text-white">Schedule Billable</a>
                        <a href="/admin/reports/payment_deposits" class="submenu-item block text-sm text-gray-400 hover:text-white">Payment Deposits</a>
                        <a href="/admin/reports/kpi_by_month" class="submenu-item block text-sm text-gray-400 hover:text-white">KPI by Month</a>
                        <a href="/admin/reports/kpi_by_patient" class="submenu-item block text-sm text-gray-400 hover:text-white">KPI by Patient</a>
                        <a href="/admin/reports/kpi_by_insurance" class="submenu-item block text-sm text-gray-400 hover:text-white">KPI by Insurance</a>
                    </div>
                </div>
                
                <!-- Excel Reports -->
                <div class="menu-group">
                    <button class="submenu-toggle menu-item flex items-center justify-between w-full text-gray-300 hover:text-white">
                        <div class="flex items-center space-x-3">
                            <iconify-icon icon="fa-solid:file-excel" class="text-xl text-green-400"></iconify-icon>
                            <span class="font-medium">Excel Reports</span>
                        </div>
                        <iconify-icon icon="lucide:chevron-down" class="chevron transition-transform duration-200"></iconify-icon>
                    </button>
                    <div class="submenu hidden ml-6 mt-2 space-y-1">
                        <a href="/admin/reports/ar_ledger_with_balance" class="submenu-item block text-sm text-gray-400 hover:text-white">AR Ledger with Balance</a>
                        <a href="/admin/reports/max_total_auth_total" class="submenu-item block text-sm text-gray-400 hover:text-white">Max Total Auth Utilization</a>
                        <a href="/admin/reports/appointment_ledger" class="submenu-item block text-sm text-gray-400 hover:text-white">Appointment Ledger</a>
                        <a href="/admin/reports/service_payroll_detail" class="submenu-item block text-sm text-gray-400 hover:text-white">Service Payroll (Detail)</a>
                        <a href="/admin/reports/service_payroll_summary" class="submenu-item block text-sm text-gray-400 hover:text-white">Service Payroll (Summary)</a>
                        <a href="/admin/reports/ratewise_payroll_detail" class="submenu-item block text-sm text-gray-400 hover:text-white">Rate Wise Payroll (Detail)</a>
                        <a href="/admin/reports/ratewise_payroll_summary" class="submenu-item block text-sm text-gray-400 hover:text-white">Rate Wise Payroll (Summary)</a>
                        <a href="/admin/reports/appointment_billed" class="submenu-item block text-sm text-gray-400 hover:text-white">Appointment Billed Report</a>
                        <a href="/admin/reports/aba_hour_client" class="submenu-item block text-sm text-gray-400 hover:text-white">ABA Hours (Client)</a>
                        <a href="/admin/reports/aba_hour_provider" class="submenu-item block text-sm text-gray-400 hover:text-white">ABA Hours (Provider)</a>
                        <a href="/admin/reports/exprected_actual_pr" class="submenu-item block text-sm text-gray-400 hover:text-white">Expected vs Actual PR</a>
                    </div>
                </div>
            </div>
            
            <!-- User Profile & Logout -->
            <div class="p-4 border-t border-gray-600">
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-xl">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                            <iconify-icon icon="lucide:user" class="text-lg"></iconify-icon>
                        </div>
                        <div>
                            <p class="text-sm font-medium">Admin User</p>
                            <p class="text-xs text-gray-400">Administrator</p>
                        </div>
                    </div>
                    <form action="/logout" method="post" class="inline">
                        <button type="submit" class="p-2 text-gray-400 hover:text-red-400 transition-colors">
                            <iconify-icon icon="ic:outline-logout" class="text-lg"></iconify-icon>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content ml-80 min-h-screen">
        <!-- Top Bar -->
        <div class="glass border-b border-gray-200 p-4 sticky top-0 z-30">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button id="openSidebar" class="md:hidden p-2 hover:bg-gray-100 rounded-lg">
                        <iconify-icon icon="lucide:menu" class="text-xl"></iconify-icon>
                    </button>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{% block page_title %}Reports Dashboard{% endblock %}</h1>
                        <p class="text-gray-600">{% block page_description %}Manage and view your therapy practice reports{% endblock %}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="hidden sm:block text-right">
                        <p class="text-sm font-medium text-gray-900">{{ current_time or "Today" }}</p>
                        <p class="text-xs text-gray-500">Last updated</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Page Content -->
        <div class="p-4 md:p-6">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <script>
        // Mobile sidebar functionality
        const sidebar = document.getElementById('sidebar');
        const openSidebar = document.getElementById('openSidebar');
        const closeSidebar = document.getElementById('closeSidebar');
        const mobileOverlay = document.getElementById('mobileOverlay');
        
        openSidebar?.addEventListener('click', () => {
            sidebar.classList.add('open');
            mobileOverlay.classList.remove('hidden');
        });
        
        closeSidebar?.addEventListener('click', () => {
            sidebar.classList.remove('open');
            mobileOverlay.classList.add('hidden');
        });
        
        mobileOverlay?.addEventListener('click', () => {
            sidebar.classList.remove('open');
            mobileOverlay.classList.add('hidden');
        });
        
        // Submenu functionality
        document.querySelectorAll('.submenu-toggle').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const submenu = toggle.nextElementSibling;
                const chevron = toggle.querySelector('.chevron');
                
                if (submenu.classList.contains('hidden')) {
                    submenu.classList.remove('hidden');
                    chevron.style.transform = 'rotate(180deg)';
                } else {
                    submenu.classList.add('hidden');
                    chevron.style.transform = 'rotate(0deg)';
                }
            });
        });
        
        // Highlight active menu item
        const currentPath = window.location.pathname;
        document.querySelectorAll('a[href]').forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
                
                // Open parent submenu
                const parentSubmenu = link.closest('.submenu');
                if (parentSubmenu) {
                    parentSubmenu.classList.remove('hidden');
                    const parentToggle = parentSubmenu.previousElementSibling;
                    const chevron = parentToggle.querySelector('.chevron');
                    chevron.style.transform = 'rotate(180deg)';
                }
            }
        });
        
        // Touch gestures for mobile
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });
        
        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });
        
        document.addEventListener('touchend', () => {
            if (!isDragging) return;
            isDragging = false;
            
            const diffX = currentX - startX;
            
            // Swipe right to open sidebar
            if (diffX > 50 && startX < 50) {
                sidebar.classList.add('open');
                mobileOverlay.classList.remove('hidden');
            }
            
            // Swipe left to close sidebar
            if (diffX < -50 && sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
                mobileOverlay.classList.add('hidden');
            }
        });
    </script>
</body>
</html>