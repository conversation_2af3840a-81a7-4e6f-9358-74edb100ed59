from fastapi import APIRouter, HTTPException, Depends, status, Request, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
from decouple import config
from ..models.auth import LoginRequest, User, Token
from ..database.connection import db
import bcrypt
from passlib.hash import bcrypt as passlib_bcrypt
import logging

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
logger = logging.getLogger(__name__)

SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config('ALGORITHM')
ACCESS_TOKEN_EXPIRE_MINUTES = int(config('ACCESS_TOKEN_EXPIRE_MINUTES'))

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

async def authenticate_user(email: str, password: str, id_token: str = "0"):
    try:
        # Validate input
        if not email:
            logger.info('No email provided')
            return False
        
        # Query user from main database
        conn = db.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM admins WHERE email = %s"
        cursor.execute(query, (email,))
        user = cursor.fetchone()
        
        if not user:
            logger.info('User not found in main database')
            return False
        
        user_dict = dict(user)
        hashed_password = user_dict['password']
        
        # Check if user belongs to Thera database
        if user_dict['id'] == 35 or user_dict.get('up_admin_id') == 35:
            try:
                # Connect to thera database
                thera_conn = db.get_connection(use_thera=True)
                thera_cursor = thera_conn.cursor()
                
                thera_query = "SELECT * FROM admins WHERE email = %s"
                thera_cursor.execute(thera_query, (email,))
                thera_user = thera_cursor.fetchone()
                
                if not thera_user:
                    logger.info('User not found in thera database')
                    return False
                
                user_dict = dict(thera_user)
                hashed_password = user_dict['password']
            except Exception as thera_error:
                logger.error(f'Thera database error: {thera_error}')
                return False
        
        # Handle bcrypt hash format compatibility
        if hashed_password:
            hashed_password = hashed_password.replace('$2y$', '$2a$')
        
        # Password verification for normal login
        if id_token == "0":
            if not password:
                logger.info('No password provided')
                return False
            
            try:
                # Use passlib for bcrypt verification (compatible with Next.js bcrypt)
                is_correct_password = pwd_context.verify(password, hashed_password)
                
                if not is_correct_password:
                    logger.info('Invalid password')
                    return False
            except Exception as bcrypt_error:
                logger.error(f'Bcrypt comparison error: {bcrypt_error}')
                return False
        
        # Return user object (remove sensitive data)
        user_without_password = {k: v for k, v in user_dict.items() if k != 'password'}
        return user_without_password
        
    except Exception as error:
        logger.error(f'Authorization error: {error}')
        return False

@router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@router.post("/login")
async def login(request: Request, email: str = Form(...), password: str = Form(...), id_token: str = Form(default="0")):
    user = await authenticate_user(email, password, id_token)
    if not user:
        # Return to login page with error message
        return templates.TemplateResponse("login.html", {
            "request": request, 
            "error": "Invalid credentials!"
        })
    
    access_token = create_access_token(data={"sub": user["email"], "user_id": user["id"]})
    response = RedirectResponse(url="/", status_code=302)
    response.set_cookie(key="access_token", value=access_token, httponly=True)
    return response

@router.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="access_token")
    return response