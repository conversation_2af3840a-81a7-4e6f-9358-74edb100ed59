# FastAPI Reports Dashboard - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- PostgreSQL database access
- Virtual environment (recommended)

### Installation Steps

1. **Clone/Navigate to Project**
   ```bash
   cd c:\Users\<USER>\projects\reports-fastapi
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure Environment**
   - Update `.env` file with your database credentials
   - Ensure database is accessible

5. **Start Application**
   ```bash
   # Option 1: Using the batch file
   start.bat
   
   # Option 2: Using uvicorn directly
   uvicorn app.main:app --reload --port 8003
   
   # Option 3: Using the run script
   python run.py
   ```

6. **Access Application**
   - Open browser to `http://localhost:8003`
   - Login with your admin credentials

## 🔧 Configuration

### Environment Variables (.env)
```env
DB_NAME=therapypms_stage
DB_USERNAME=readonly_user_suhaib
DB_PASSWORD="2z#<7£EQ15C7nQY1"
DB_HOST=************
DB_PORT=54320

SECRET_KEY=YmFzZTY0LWVuY29kZWQtc2VjcmV0LXN0cmluZw==
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### Database Requirements
- PostgreSQL 12+
- Tables: `admins`, `employees`, `clients`, `appoinments`, etc.
- Read access to all reporting tables

## 📁 Project Structure
```
reports-fastapi/
├── app/
│   ├── database/connection.py      # DB connection
│   ├── models/auth.py             # Data models
│   ├── routers/                   # API routes
│   │   ├── auth.py               # Authentication
│   │   ├── reports.py            # Basic reports
│   │   ├── additional_reports.py # Extended reports
│   │   ├── complete_reports.py   # Complex reports
│   │   └── final_reports.py      # Final reports
│   ├── templates/                # HTML templates
│   │   ├── base.html            # Base template
│   │   ├── dashboard.html       # Main dashboard
│   │   ├── login.html           # Login page
│   │   └── reports/             # Report templates
│   ├── static/                  # Static files
│   └── main.py                  # FastAPI app
├── .env                         # Environment config
├── requirements.txt             # Dependencies
├── run.py                      # App runner
└── start.bat                   # Windows startup
```

## 🌐 Production Deployment

### Using Gunicorn (Linux/Mac)
```bash
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Using Docker
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /static {
        alias /path/to/app/static;
    }
}
```

## 🔒 Security Considerations

1. **Environment Variables**
   - Never commit `.env` to version control
   - Use strong SECRET_KEY in production
   - Rotate secrets regularly

2. **Database Security**
   - Use read-only database user
   - Implement connection pooling
   - Enable SSL connections

3. **Application Security**
   - Enable HTTPS in production
   - Set secure cookie flags
   - Implement rate limiting

## 📊 Monitoring & Logging

### Health Check
- Endpoint: `GET /health`
- Returns: `{"status": "healthy", "message": "Reports Dashboard is running"}`

### Logging Configuration
```python
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `.env`
   - Verify database server is running
   - Test connection manually

2. **Import Errors**
   - Ensure virtual environment is activated
   - Install all requirements: `pip install -r requirements.txt`

3. **Port Already in Use**
   - Change port: `uvicorn app.main:app --port 8004`
   - Kill existing process: `taskkill /f /im python.exe`

4. **Template Not Found**
   - Check template paths in `app/templates/`
   - Verify Jinja2 configuration

### Performance Optimization

1. **Database Optimization**
   - Add indexes on frequently queried columns
   - Use connection pooling
   - Implement query caching

2. **Application Optimization**
   - Enable gzip compression
   - Use CDN for static files
   - Implement response caching

## 📈 Scaling

### Horizontal Scaling
- Deploy multiple instances behind load balancer
- Use shared session storage (Redis)
- Implement database read replicas

### Vertical Scaling
- Increase server resources
- Optimize database queries
- Use async database drivers

## 🔄 Updates & Maintenance

### Regular Tasks
- Monitor application logs
- Update dependencies
- Backup database
- Review security settings

### Version Updates
```bash
git pull origin main
pip install -r requirements.txt
systemctl restart reports-dashboard
```