import psycopg2
from psycopg2.extras import RealDictCursor
from decouple import config
import logging

logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        self.connection = None
        self.thera_connection = None
    
    def connect(self):
        try:
            # Main database connection
            self.connection = psycopg2.connect(
                host=config('DB_HOST'),
                database=config('DB_NAME'),
                user=config('DB_USERNAME'),
                password=config('DB_PASSWORD'),
                port=config('DB_PORT'),
                cursor_factory=RealDictCursor
            )
            self.connection.autocommit = True
            logger.info("Connected to main database")
            
            # Thera database connection (same as main for this setup)
            try:
                self.thera_connection = psycopg2.connect(
                    host=config('THERA_DB_HOST'),
                    database=config('THERA_DB_NAME'),
                    user=config('THERA_DB_USERNAME'),
                    password=config('THERA_DB_PASSWORD'),
                    port=config('THERA_DB_PORT'),
                    cursor_factory=RealDictCursor
                )
                self.thera_connection.autocommit = True
                logger.info("Connected to thera database")
            except Exception as thera_error:
                logger.warning(f"Thera database connection failed: {thera_error}")
                # Use main connection as fallback
                self.thera_connection = self.connection
                
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise e
    
    def get_connection(self, use_thera=False):
        if use_thera and self.thera_connection and not self.thera_connection.closed:
            return self.thera_connection
        
        if not self.connection or self.connection.closed:
            self.connect()
        return self.connection
    
    def close(self):
        if self.connection and not self.connection.closed:
            self.connection.close()
        if self.thera_connection and not self.thera_connection.closed and self.thera_connection != self.connection:
            self.thera_connection.close()

db = Database()