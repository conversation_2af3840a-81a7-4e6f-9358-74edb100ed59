{% extends "base.html" %}

{% block title %}Missing Credentials - TherapyPMS Report{% endblock %}

{% block content %}
<div>
    <div class="rounded-md bg-white py-4 px-2 flex">
        <div class="text-md font-semibold p-2 text-tpms">
            <h2 class="border-b-4">Missing Credentials</h2>
        </div>
        <div class="flex-1">
            <div class="flex mt-1 justify-end">
                <div>
                    <button id="viewDataBtn" class="bg-tpms text-white p-1 rounded-md px-3">View Data</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AG Grid Container -->
    <div id="agGridContainer" style="display: none;">
        <div class="ag-theme-alpine grid-container mt-3">
            <div class="flex mb-2">
                <button id="exportBtn" class="border p-2 bg-white text-gray-900">Download CSV</button>
                <button id="clearFilterBtn" class="border p-2 bg-red-400 mx-2 text-white">Clear Filter</button>
            </div>
            <div id="myGrid" class="ag-grid"></div>
        </div>
    </div>
    
    <!-- Loading indicator -->
    <div id="loadingIndicator" class="hidden text-center py-8">
        <div class="loading"></div>
        <span class="ml-2">Loading data...</span>
    </div>
    
    <!-- Error message -->
    <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4">
        <span id="errorText"></span>
    </div>
</div>

<script>
let gridApi;

const columnDefs = [
    { headerName: 'Staff Name', field: "staff_name", filter: "agTextColumnFilter" },
    { headerName: 'Email', field: "office_email", filter: "agTextColumnFilter" },
    { headerName: 'NPI', field: "individual_npi", filter: "agTextColumnFilter" },
    { headerName: 'Title', field: "title", filter: "agTextColumnFilter" },
    { headerName: 'Status', field: "credential_status", filter: "agTextColumnFilter" }
];

const defaultColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true,
    floatingFilter: true
};

function showError(message) {
    document.getElementById('errorText').textContent = message;
    document.getElementById('errorMessage').classList.remove('hidden');
}

function hideError() {
    document.getElementById('errorMessage').classList.add('hidden');
}

function showLoading() {
    document.getElementById('loadingIndicator').classList.remove('hidden');
    document.getElementById('agGridContainer').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').classList.add('hidden');
}

const onGridReady = (params) => {
    gridApi = params.api;
};

const onBtnExport = () => {
    gridApi.exportDataAsCsv();
};

const clearFilters = () => {
    gridApi.setFilterModel(null);
};

document.getElementById('viewDataBtn').addEventListener('click', async () => {
    hideError();
    showLoading();
    
    try {
        const response = await fetch('/api/admin/staffs/missing_credentials', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        });
        
        hideLoading();
        
        if (response.ok) {
            const data = await response.json();
            
            if (data && data.length > 0) {
                document.getElementById('agGridContainer').style.display = 'block';
                
                const gridDiv = document.querySelector('#myGrid');
                const gridOptions = {
                    columnDefs: columnDefs,
                    rowData: data,
                    defaultColDef: defaultColDef,
                    suppressExcelExport: true,
                    onGridReady: onGridReady,
                    pagination: true,
                    paginationPageSize: 10,
                    paginationPageSizeSelector: [10, 25, 50, 100, 1000],
                    suppressAndOrCondition: true
                };
                
                if (gridApi) {
                    gridApi.destroy();
                }
                
                gridApi = agGrid.createGrid(gridDiv, gridOptions);
            } else {
                showError('No data found');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            showError(errorData.detail || 'Something went wrong!');
        }
    } catch (error) {
        hideLoading();
        console.error('Error:', error);
        showError('Something went wrong!');
    }
});

document.getElementById('exportBtn').addEventListener('click', onBtnExport);
document.getElementById('clearFilterBtn').addEventListener('click', clearFilters);
</script>
{% endblock %}