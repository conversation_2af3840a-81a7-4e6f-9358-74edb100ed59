from fastapi import APIRouter, Request, Depends, HTTPException, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jin<PERSON>2Templates
from jose import JWTError, jwt
from decouple import config
from datetime import datetime, timedelta
from ..database.connection import db
from typing import Optional

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")

SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config('ALGORITHM')

def get_current_user(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_admin_info(user_id: int):
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT is_up_admin, up_admin_id FROM admins WHERE id = %s", (user_id,))
    user_info = cursor.fetchone()
    admin_id = user_id if user_info['is_up_admin'] == 1 else user_info['up_admin_id']
    return admin_id

def get_client_name_query(admin_id: int):
    if admin_id == 35:
        return 'c.client_full_name'
    else:
        return """(CASE
                    WHEN (c.client_last_name IS NOT NULL) AND (c.client_first_name IS NOT NULL)
                    THEN CONCAT(c.client_last_name,', ',c.client_first_name)
                    ELSE c.client_full_name
                  END)"""

def get_staff_name_query(admin_id: int):
    if admin_id == 35:
        return 'e.full_name'
    else:
        return """(CASE
                    WHEN (e.last_name IS NOT NULL) AND (e.first_name IS NOT NULL)
                    THEN CONCAT(e.last_name,', ',e.first_name)
                    ELSE e.full_name
                  END)"""

# Staff Reports
@router.post("/api/admin/staffs/active_staff")
async def get_active_staff(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    query = """
        SELECT e.last_name, e.first_name, e.office_phone, 
        to_char(cast(e.hir_date_compnay as date), 'MM/DD/YYYY') as hir_date_compnay, 
        e.title, 
        to_char(cast(e.terminated_date as date), 'MM/DD/YYYY') as terminated_date, 
        e.office_email, eta.type_name,
        to_char(cast(e.staff_birthday as date), 'MM/DD/YYYY') as staff_birthday, 
        e.individual_npi, 
        concat(e.qb_id,e.gusto_id) as payroll_parter_id, eos.degree_level, 
        ecd.mobile as phone_cell, ecd.address_one, ecd.address_two, 
        ecd.city, ecd.state, ecd.zip, ecd.main_phone, e.language, e.id as staffid,
        'Yes' as active, e.notes
        from employees as e
        left join employee_type_assigns as eta on eta.id=e.credential_type
        left join employee_other_setups as eos on eos.employee_id=e.id
        left join employee_contact_details as ecd on ecd.employee_id=e.id
        WHERE e.admin_id=%s and e.is_active = 1
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

@router.post("/api/admin/staffs/missing_credentials")
async def get_missing_credentials(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    staff_name_query = get_staff_name_query(admin_id)
    
    query = f"""
        SELECT {staff_name_query} as staff_name, e.office_email, 
        e.individual_npi, e.title, 'Missing' as credential_status
        FROM employees as e
        WHERE e.admin_id=%s and e.is_active = 1 
        AND (e.individual_npi IS NULL OR e.individual_npi = '')
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Patient Reports
@router.post("/api/admin/patients/expired_auth")
async def get_expired_auth(request: Request, 
                          start_date: Optional[str] = Form(None),
                          end_date: Optional[str] = Form(None),
                          user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    # Default date range if not provided
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    staff_name_query = get_staff_name_query(admin_id)
    
    query = f"""
        WITH PatientStatus AS (select id, admin_id from client_statuses where is_active=1)
        select ca.id, ca.client_id, ca.payor_id, ca.end_date, ca.authorization_number, 
        ca.supervisor_id, ap.payor_name,
        {client_name_query} as client_fullname, c.is_active_client, 
        {staff_name_query} as supervisor_name,
        to_char(cast(ca.end_date as date), 'MM/DD/YYYY') as end_date, 
        ca.authorization_number,
        (CASE
            WHEN (PatientStatus.id IS NOT NULL) OR (c.is_active_client = 1)
            THEN 'Active'
            ELSE 'In-Active'
        END) as patient_status
        from client_authorizations as ca 
        left join clients as c on c.id=ca.client_id
        left join PatientStatus on PatientStatus.id = c.is_active_client and PatientStatus.admin_id = %s 
        left join employees as e on e.id=ca.supervisor_id
        left join all_payors as ap on ap.id = ca.payor_id
        where ca.admin_id = %s and ca.end_date between %s and %s 
        and ca.is_valid = 1 and ca.is_deleted is null 
        order by ca.end_date desc
    """
    
    cursor.execute(query, (admin_id, admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

@router.post("/api/admin/patients/without_auth")
async def get_without_auth(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT c.id as client_id, {client_name_query} as client_fullname,
        c.client_first_name, c.client_last_name, 'Missing Authorization' as status
        FROM clients as c
        LEFT JOIN client_authorizations as ca ON ca.client_id = c.id AND ca.is_valid = 1
        WHERE c.admin_id = %s AND c.is_active_client = 1 AND ca.id IS NULL
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Appointment Reports
@router.post("/api/admin/appointments/scheduled_not_rendered")
async def get_scheduled_not_rendered(request: Request, 
                                   start_date: Optional[str] = Form(None),
                                   end_date: Optional[str] = Form(None),
                                   user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    staff_name_query = get_staff_name_query(admin_id)
    
    query = f"""
        SELECT a.id, {client_name_query} as client_name, 
        {staff_name_query} as provider_name,
        to_char(cast(a.appointment_date as date), 'MM/DD/YYYY') as appointment_date,
        a.start_time, a.end_time, s.service_name, 'Not Rendered' as status
        FROM appointments as a
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN employees as e ON e.id = a.provider_id
        LEFT JOIN services as s ON s.id = a.service_id
        WHERE a.admin_id = %s 
        AND a.appointment_date BETWEEN %s AND %s
        AND a.is_rendered = 0
        ORDER BY a.appointment_date DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Financial Reports
@router.post("/api/admin/reports/schedule_billable")
async def get_schedule_billable(request: Request, 
                              start_date: Optional[str] = Form(None),
                              end_date: Optional[str] = Form(None),
                              user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    staff_name_query = get_staff_name_query(admin_id)
    
    query = f"""
        SELECT {client_name_query} as client_name, 
        {staff_name_query} as provider_name,
        s.service_name, COUNT(*) as total_sessions,
        SUM(a.billable_units) as total_units,
        to_char(cast(a.appointment_date as date), 'MM/DD/YYYY') as appointment_date
        FROM appointments as a
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN employees as e ON e.id = a.provider_id
        LEFT JOIN services as s ON s.id = a.service_id
        WHERE a.admin_id = %s 
        AND a.appointment_date BETWEEN %s AND %s
        AND a.is_billable = 1
        GROUP BY c.id, e.id, s.id, a.appointment_date
        ORDER BY a.appointment_date DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])