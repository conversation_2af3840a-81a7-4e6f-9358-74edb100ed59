@echo off
echo ========================================
echo    Reports Dashboard - FastAPI
echo ========================================
echo.

echo Testing database connection...
python test_db.py
if %ERRORLEVEL% NEQ 0 (
    echo Database connection failed! Please check your .env file.
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
pip install -r requirements.txt

echo.
echo Starting FastAPI server on port 8003...
echo Access the application at: http://localhost:8003
echo Press Ctrl+C to stop the server
echo.

uvicorn app.main:app --reload --port 8003 --host 0.0.0.0
pause