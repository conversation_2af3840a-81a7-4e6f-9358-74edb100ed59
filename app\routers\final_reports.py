from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import JSONResponse
from jose import JWTError, jwt
from decouple import config
from datetime import datetime, timedelta
from ..database.connection import db
from typing import Optional

router = APIRouter()

SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config('ALGORITHM')

def get_current_user(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_admin_info(user_id: int):
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT is_up_admin, up_admin_id FROM admins WHERE id = %s", (user_id,))
    user_info = cursor.fetchone()
    admin_id = user_id if user_info['is_up_admin'] == 1 else user_info['up_admin_id']
    return admin_id

def get_client_name_query(admin_id: int):
    if admin_id == 35:
        return 'c.client_full_name'
    else:
        return """(CASE
                    WHEN (c.client_last_name IS NOT NULL) AND (c.client_first_name IS NOT NULL)
                    THEN CONCAT(c.client_last_name,', ',c.client_first_name)
                    ELSE c.client_full_name
                  END)"""

def get_staff_name_query(admin_id: int):
    if admin_id == 35:
        return 'e.full_name'
    else:
        return """(CASE
                    WHEN (e.last_name IS NOT NULL) AND (e.first_name IS NOT NULL)
                    THEN CONCAT(e.last_name,', ',e.first_name)
                    ELSE e.full_name
                  END)"""

# Session Note Missing
@router.post("/api/admin/appointments/session_note_missing")
async def get_session_note_missing(request: Request, 
                                 start_date: Optional[str] = Form(None),
                                 end_date: Optional[str] = Form(None),
                                 user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    staff_name_query = get_staff_name_query(admin_id)
    today_date = datetime.now().strftime('%Y-%m-%d')
    
    query = f"""
        SELECT {client_name_query} as client_full_name, 
        {staff_name_query} as full_name, 
        a.status, a.time_duration,
        TO_CHAR(CAST(a.schedule_date AS DATE), 'MM/DD/YYYY') as schedule_date,
        CONCAT(TO_CHAR(CAST(a.from_time AS TIME), 'HH12:MI AM'),' - ',
               TO_CHAR(CAST(a.to_time AS TIME), 'HH12:MI AM')) as from_to_time,
        TO_CHAR(CAST(a.schedule_date AS DATE), 'MM/DD/YYYY') as dos
        FROM appoinments as a
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN session_notes_avails as sna ON sna.session_id = a.id
        LEFT JOIN api_session_files as asf ON asf.session_id = a.id
        LEFT JOIN employees as e ON e.id = a.provider_id
        WHERE a.admin_id = %s
        AND a.schedule_date BETWEEN %s AND %s
        AND sna.session_id IS NULL
        AND asf.session_id IS NULL
        AND a.schedule_date < %s
        AND a.billable = 1
        AND a.status NOT IN ('Cancelled by Client','Cancelled by Provider','CC more than 24 hrs',
                            'CC less than 24 hrs','No Show','Canceled - Rescheduled')
        AND a.status != 'deleted'
        ORDER BY a.schedule_date DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date, today_date))
    results = cursor.fetchall()
    
    row_result = []
    for row in results:
        hours = (row['time_duration'] / 60) if row['time_duration'] else 0
        service = f"Service ({hours:.2f} Hrs)"
        
        row_result.append({
            'client_full_name': row['client_full_name'],
            'schedule_date': row['schedule_date'],
            'from_to_time': row['from_to_time'],
            'full_name': row['full_name'],
            'service_name': service,
            'status': row['status']
        })
    
    return JSONResponse(content=row_result)

# Session Unlocked Notes
@router.post("/api/admin/appointments/session_unlocked_notes")
async def get_session_unlocked_notes(request: Request, 
                                   start_date: Optional[str] = Form(None),
                                   end_date: Optional[str] = Form(None),
                                   user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    staff_name_query = get_staff_name_query(admin_id)
    
    query = f"""
        SELECT {client_name_query} as client_full_name, 
        {staff_name_query} as full_name,
        TO_CHAR(CAST(a.schedule_date AS DATE), 'MM/DD/YYYY') as schedule_date,
        CONCAT(TO_CHAR(CAST(a.from_time AS TIME), 'HH12:MI AM'),' - ',
               TO_CHAR(CAST(a.to_time AS TIME), 'HH12:MI AM')) as from_to_time,
        sna.is_locked, 'Unlocked' as note_status
        FROM appoinments as a
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN employees as e ON e.id = a.provider_id
        LEFT JOIN session_notes_avails as sna ON sna.session_id = a.id
        WHERE a.admin_id = %s
        AND a.schedule_date BETWEEN %s AND %s
        AND sna.is_locked = 0
        AND a.status = 'Rendered'
        AND a.status != 'deleted'
        ORDER BY a.schedule_date DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Appointment Ledger
@router.post("/api/admin/reports/appointment_ledger")
async def get_appointment_ledger(request: Request, 
                               start_date: Optional[str] = Form(None),
                               end_date: Optional[str] = Form(None),
                               user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT a.id, a.authorization_activity_id, a.time_duration, a.client_id, 
        aps.cpt_code, aps.service_id,
        TO_CHAR(CAST(a.schedule_date AS DATE), 'MM/DD/YYYY') as schedule_date, 
        {client_name_query} as client_full_name 
        FROM appoinments as a 
        LEFT JOIN app_services aps ON aps.session_id = a.id 
        INNER JOIN clients as c ON c.id = a.client_id
        WHERE a.admin_id = %s AND a.schedule_date BETWEEN %s AND %s 
        AND a.billable = 1 
        AND EXISTS (SELECT * FROM processing_claims WHERE a.id = processing_claims.appointment_id 
                   AND is_mark_gen = 1 AND (is_clubbed IS NULL OR is_clubbed = 0)) 
        AND a.status != 'deleted'  
        ORDER BY LOWER(c.client_last_name) ASC, LOWER(c.client_first_name) ASC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    appointments = cursor.fetchall()
    
    row_result = []
    for appointment in appointments:
        appointment_id = appointment['id']
        service_id = appointment['service_id']
        cpt_codes = appointment['cpt_code'].split(",") if appointment['cpt_code'] else []
        
        for cpt in cpt_codes:
            # Get processing claim info
            cursor.execute("""
                SELECT id, is_clubbed, cpt, units FROM processing_claims 
                WHERE appointment_id = %s AND cpt = %s LIMIT 1
            """, (appointment_id, cpt))
            
            claim_result = cursor.fetchone()
            cpt_ledger = 'NB'
            units_ledger = 0
            
            if claim_result:
                cpt_ledger = claim_result['cpt']
                units_ledger = claim_result['units']
            
            # Calculate app units (simplified version)
            app_units = (appointment['time_duration'] / 15) if appointment['time_duration'] else 0
            
            row_result.append({
                'client_full_name': appointment['client_full_name'],
                'schedule_date': appointment['schedule_date'],
                'cpt': cpt,
                'cpt_ledger': cpt_ledger,
                'cpt_match': 'Yes' if cpt_ledger == cpt else 'No',
                'app_units': f"{app_units:.2f}",
                'units_ledger': f"{units_ledger:.2f}",
                'units_diff': f"{(app_units - units_ledger):.2f}"
            })
    
    return JSONResponse(content=row_result)

# ABA Hour Provider
@router.post("/api/admin/reports/aba_hour_provider")
async def get_aba_hour_provider(request: Request, 
                              start_date: Optional[str] = Form(None),
                              end_date: Optional[str] = Form(None),
                              user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    staff_name_query = get_staff_name_query(admin_id)
    
    # Get distinct providers with ABA services
    query = f"""
        SELECT DISTINCT a.provider_id, {staff_name_query} as provider_name 
        FROM appoinments a 
        LEFT JOIN employees e ON e.id = a.provider_id
        WHERE a.admin_id = %s AND a.schedule_date BETWEEN %s AND %s 
        AND EXISTS (SELECT * FROM app_services WHERE a.id = app_services.session_id 
                   AND cpt_code IN ('97153', '97155')) 
        AND a.status != 'deleted'
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    providers = cursor.fetchall()
    
    row_result = []
    for provider in providers:
        provider_id = provider['provider_id']
        
        # Direct hours worked
        cursor.execute("""
            SELECT SUM(COALESCE(time_duration,0)) as provider_hours 
            FROM appoinments 
            WHERE provider_id = %s 
            AND EXISTS (SELECT * FROM app_services WHERE appoinments.id = app_services.session_id 
                       AND cpt_code = '97153') 
            AND schedule_date BETWEEN %s AND %s 
            AND status = 'Rendered' AND status != 'deleted'
        """, (provider_id, start_date, end_date))
        
        worked_direct_result = cursor.fetchone()
        worked_direct = (worked_direct_result['provider_hours'] / 60) if worked_direct_result['provider_hours'] else 0
        
        # Supervision hours worked
        cursor.execute("""
            SELECT SUM(COALESCE(time_duration,0)) as provider_hours 
            FROM appoinments a 
            INNER JOIN app_services aps ON aps.session_id = a.id AND aps.cpt_code = '97155'
            WHERE a.provider_id = %s AND a.schedule_date BETWEEN %s AND %s 
            AND a.status = 'Rendered'
        """, (provider_id, start_date, end_date))
        
        worked_supervision_result = cursor.fetchone()
        worked_supervision = (worked_supervision_result['provider_hours'] / 60) if worked_supervision_result['provider_hours'] else 0
        
        # Calculate percentage
        worked_percentage = (worked_supervision / worked_direct * 100) if worked_direct > 0 else 0
        
        row_result.append({
            'provider_name': provider['provider_name'],
            'worked_direct': f"{worked_direct:.2f}",
            'worked_supervision': f"{worked_supervision:.2f}",
            'worked_percentage': f"{worked_percentage:.1f}",
            'total_hours': f"{(worked_direct + worked_supervision):.2f}"
        })
    
    return JSONResponse(content=row_result)

# Max Total Auth Utilization
@router.post("/api/admin/reports/max_total_auth_total")
async def get_max_total_auth_total(request: Request, 
                                 start_date: Optional[str] = Form(None),
                                 end_date: Optional[str] = Form(None),
                                 user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT ca.client_id, {client_name_query} as client_name,
        ca.authorization_number, ap.payor_name,
        ca.max_total_auth, ca.total_used_auth,
        (ca.max_total_auth - ca.total_used_auth) as remaining_auth,
        CASE 
            WHEN ca.max_total_auth > 0 
            THEN (ca.total_used_auth / ca.max_total_auth * 100)
            ELSE 0 
        END as utilization_percentage,
        TO_CHAR(CAST(ca.start_date AS DATE), 'MM/DD/YYYY') as start_date,
        TO_CHAR(CAST(ca.end_date AS DATE), 'MM/DD/YYYY') as end_date
        FROM client_authorizations as ca
        LEFT JOIN clients as c ON c.id = ca.client_id
        LEFT JOIN all_payors as ap ON ap.id = ca.payor_id
        WHERE ca.admin_id = %s 
        AND ca.end_date BETWEEN %s AND %s
        AND ca.is_valid = 1 AND ca.is_deleted IS NULL
        AND ca.max_total_auth > 0
        ORDER BY utilization_percentage DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Ratewise Payroll Summary
@router.post("/api/admin/reports/ratewise_payroll_summary")
async def get_ratewise_payroll_summary(request: Request, 
                                     start_date: Optional[str] = Form(None),
                                     end_date: Optional[str] = Form(None),
                                     user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    staff_name_query = get_staff_name_query(admin_id)
    
    query = f"""
        SELECT {staff_name_query} as provider_name,
        er.hourly_rate,
        SUM(a.time_duration / 60.0) as total_hours,
        SUM((a.time_duration / 60.0) * er.hourly_rate) as total_pay,
        COUNT(*) as total_sessions
        FROM appoinments as a
        LEFT JOIN employees as e ON e.id = a.provider_id
        LEFT JOIN employee_rates as er ON er.employee_id = a.provider_id
        WHERE a.admin_id = %s AND a.schedule_date BETWEEN %s AND %s
        AND a.status = 'Rendered'
        GROUP BY e.id, e.first_name, e.last_name, e.full_name, er.hourly_rate
        ORDER BY total_pay DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Appointment Billed
@router.post("/api/admin/reports/appointment_billed")
async def get_appointment_billed(request: Request, 
                               start_date: Optional[str] = Form(None),
                               end_date: Optional[str] = Form(None),
                               user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    staff_name_query = get_staff_name_query(admin_id)
    
    query = f"""
        SELECT {client_name_query} as client_name,
        {staff_name_query} as provider_name,
        TO_CHAR(CAST(a.schedule_date AS DATE), 'MM/DD/YYYY') as service_date,
        aps.cpt_code, aps.service_name,
        a.time_duration / 60.0 as hours,
        CASE WHEN pc.id IS NOT NULL THEN 'Billed' ELSE 'Not Billed' END as billing_status,
        pc.billed_amount, pc.units
        FROM appoinments as a
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN employees as e ON e.id = a.provider_id
        LEFT JOIN app_services as aps ON aps.session_id = a.id
        LEFT JOIN processing_claims as pc ON pc.appointment_id = a.id
        WHERE a.admin_id = %s AND a.schedule_date BETWEEN %s AND %s
        AND a.status = 'Rendered' AND a.billable = 1
        ORDER BY a.schedule_date DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])