# FastAPI Reports Dashboard - API Endpoints

## Authentication Endpoints
- `GET /login` - Login page
- `POST /login` - User authentication
- `POST /logout` - User logout

## Staff Reports
- `POST /api/admin/staffs/active_staff` - Active staff details
- `POST /api/admin/staffs/missing_credentials` - Staff with missing credentials
- `POST /api/admin/staffs/expiring_credentials` - Staff with expiring credentials
- `POST /api/admin/staffs/time_of_mgmt` - Time off management
- `POST /api/admin/staffs/provider_missing_sign` - Providers missing signatures

## Patient Reports
- `POST /api/admin/patients/expired_auth` - Expired authorizations (requires date filters)
- `POST /api/admin/patients/expiring_auth` - Expiring authorizations (requires date filters)
- `POST /api/admin/patients/without_auth` - Patients without authorization
- `POST /api/admin/patients/expiring_doc` - Expiring documents
- `POST /api/admin/patients/auth_placeholder` - Authorization placeholders
- `POST /api/admin/patients/non_payor_tag` - Patient/Guarantor pay clients
- `POST /api/admin/patients/arrived_info` - Patient arrival information

## Appointment/Scheduler Reports
- `POST /api/admin/appointments/scheduled_not_rendered` - Scheduled but not rendered (requires date filters)
- `POST /api/admin/appointments/scheduled_not_attended` - Sessions not attended (requires date filters)
- `POST /api/admin/appointments/session_missing_signature` - Sessions missing signatures (requires date filters)
- `POST /api/admin/appointments/session_note_missing` - Sessions with missing notes (requires date filters)
- `POST /api/admin/appointments/session_unlocked_notes` - Sessions with unlocked notes (requires date filters)

## Financial/KPI Reports
- `POST /api/admin/reports/schedule_billable` - Schedule billable report (requires date filters)
- `POST /api/admin/reports/payment_deposits` - Payment deposits (requires date filters)
- `POST /api/admin/reports/kpi_by_month` - KPI report by month (requires date filters)
- `POST /api/admin/reports/kpi_by_patient` - KPI report by patient (requires date filters)
- `POST /api/admin/reports/kpi_by_insurance` - KPI report by insurance (requires date filters)

## ABA Hour Reports
- `POST /api/admin/reports/aba_hour_client` - ABA hours by client (requires date filters)
- `POST /api/admin/reports/aba_hour_provider` - ABA hours by provider (requires date filters)

## Ledger/Billing Reports
- `POST /api/admin/reports/ar_ledger_with_balance` - AR ledger with balance (requires date filters)
- `POST /api/admin/reports/appointment_ledger` - Appointment vs ledger report (requires date filters)
- `POST /api/admin/reports/appointment_billed` - Appointment billed report (requires date filters)
- `POST /api/admin/reports/max_total_auth_total` - Max total auth utilization (requires date filters)

## Payroll Reports
- `POST /api/admin/reports/service_payroll_detail` - Service wise payroll detail (requires date filters)
- `POST /api/admin/reports/service_payroll_summary` - Service wise payroll summary (requires date filters)
- `POST /api/admin/reports/ratewise_payroll_detail` - Rate wise payroll detail (requires date filters)
- `POST /api/admin/reports/ratewise_payroll_summary` - Rate wise payroll summary (requires date filters)

## Expected PR Reports
- `POST /api/admin/reports/exprected_actual_pr` - Expected vs actual PR report (requires date filters)

## Request Format
For endpoints requiring date filters, send form data:
```
start_date: YYYY-MM-DD
end_date: YYYY-MM-DD
```

## Response Format
All endpoints return JSON arrays with report data:
```json
[
  {
    "field1": "value1",
    "field2": "value2",
    ...
  }
]
```

## Authentication
All API endpoints require authentication via session cookies set during login.