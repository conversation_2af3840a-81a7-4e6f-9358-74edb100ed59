<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - TherapyPMS Reports</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.iconify.design/3/3.1.1/iconify.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Inter', sans-serif; }
        .bg-primary { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); }
        .glass { backdrop-filter: blur(16px) saturate(180%); background-color: rgba(255, 255, 255, 0.8); border: 1px solid rgba(255, 255, 255, 0.3); }
        .login-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .floating { animation: float 6s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
    </style>
</head>
<body class="min-h-screen login-bg flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full floating"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full floating" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-10 rounded-full floating" style="animation-delay: -1s;"></div>
    </div>
    
    <!-- Login Card -->
    <div class="glass rounded-3xl p-8 w-full max-w-md relative z-10">
        <!-- Logo & Header -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-4">
                <iconify-icon icon="medical-icon:i-health-services" class="text-3xl text-white"></iconify-icon>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h1>
            <p class="text-gray-600">Sign in to your TherapyPMS Reports account</p>
        </div>
        
        <!-- Error Message -->
        {% if error %}
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
            <div class="flex items-center space-x-2">
                <iconify-icon icon="lucide:alert-circle" class="text-red-500"></iconify-icon>
                <p class="text-red-700 text-sm">{{ error }}</p>
            </div>
        </div>
        {% endif %}
        
        <!-- Login Form -->
        <form action="/login" method="post" class="space-y-6">
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <iconify-icon icon="lucide:mail" class="text-gray-400"></iconify-icon>
                    </div>
                    <input
                        id="email"
                        name="email"
                        type="email"
                        autocomplete="email"
                        required
                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                        placeholder="Enter your email"
                    />
                </div>
            </div>
            
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    Password
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <iconify-icon icon="lucide:lock" class="text-gray-400"></iconify-icon>
                    </div>
                    <input
                        id="password"
                        name="password"
                        type="password"
                        autocomplete="current-password"
                        required
                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                        placeholder="Enter your password"
                    />
                </div>
            </div>
            
            <input type="hidden" name="id_token" value="0">
            
            <button
                type="submit"
                class="w-full bg-primary text-white py-3 px-4 rounded-xl font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all transform hover:scale-[1.02]"
            >
                Sign In
            </button>
        </form>
        
        <!-- Footer -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <p class="text-xs text-gray-500 text-center leading-relaxed">
                ABA TherapyPMS respects the privacy of our users and values their trust. 
                Please read our privacy policy carefully. If you do not agree with the terms 
                of our privacy policy, then please do not access the site.
            </p>
        </div>
    </div>
    
    <!-- Loading Animation -->
    <script>
        document.querySelector('form').addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            button.innerHTML = '<iconify-icon icon="lucide:loader-2" class="animate-spin mr-2"></iconify-icon>Signing In...';
            button.disabled = true;
        });
    </script>
</body>
</html>