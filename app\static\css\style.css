@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body,
:root {
  height: 100%;
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

.grid-container {
  height: 590px;
  width: 100%;
}

.ag-grid .ag-cell {
  padding: 0.5rem;
  color: #4b5563;
  border-color: #e5e7eb;
  line-height: 1.5;
}

.ag-grid .ag-header-cell {
  font-weight: 600;
  color: #374151;
  background-color: #f9fafb;
  border-color: #e5e7eb;
}

.tpms {
  color: #1e40af;
}

.bg-tpms {
  background-color: #1e40af;
}

.text-tpms {
  color: #1e40af;
}

.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}