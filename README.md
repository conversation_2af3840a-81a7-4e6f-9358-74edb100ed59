# Reports Dashboard - FastAPI

A FastAPI-based reports dashboard converted from Next.js, providing comprehensive reporting for therapy practice management.

## 🚀 Quick Start

1. **Navigate to project:**
   ```bash
   cd c:\Users\<USER>\projects\reports-fastapi
   ```

2. **Run the application:**
   ```bash
   start.bat
   ```
   Or manually:
   ```bash
   pip install -r requirements.txt
   uvicorn app.main:app --reload --port 8003
   ```

3. **Access:** `http://localhost:8003`

## 📋 Requirements

- Python 3.8+
- PostgreSQL database access
- Dependencies in `requirements.txt`

## 🔧 Configuration

Update `.env` file with your database credentials:
```env
DB_NAME=therapypms_stage
DB_USERNAME=readonly_user_suhaib
DB_PASSWORD="2z#<7£EQ15C7nQY1"
DB_HOST=************
DB_PORT=54320

SECRET_KEY=YmFzZTY0LWVuY29kZWQtc2VjcmV0LXN0cmluZw==
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 📊 Available Reports

### Staff Reports
- Active Staff Details
- Missing Credentials
- Expiring Credentials
- Time Off Management

### Patient Reports
- Expired Authorizations
- Expiring Authorization
- Authorization Missing
- Expiring Documents

### Scheduler Reports
- Scheduled Not Rendered
- Sessions Not Attended
- Missing Signatures

### Financial Reports
- Schedule Billable
- Payment Deposits
- KPI Reports

## 🐛 Troubleshooting

### "Error loading data" Issue

1. **Check Database Connection:**
   ```bash
   python test_db.py
   ```

2. **Verify Environment Variables:**
   - Ensure `.env` file exists
   - Check database credentials
   - Verify database server is accessible

3. **Check Application Logs:**
   - Look for error messages in console
   - Check browser developer tools (F12)

4. **Common Fixes:**
   ```bash
   # Reinstall dependencies
   pip install -r requirements.txt
   
   # Test specific endpoint
   curl -X POST http://localhost:8003/api/admin/staffs/active_staff
   ```

### Database Connection Issues

1. **Test connection manually:**
   ```python
   import psycopg2
   conn = psycopg2.connect(
       host="************",
       database="therapypms_stage", 
       user="readonly_user_suhaib",
       password="2z#<7£EQ15C7nQY1",
       port=54320
   )
   ```

2. **Check firewall/network access**
3. **Verify database server is running**

### Authentication Issues

1. **Clear browser cookies**
2. **Check SECRET_KEY in .env**
3. **Verify admin table exists**

## 🔍 API Testing

Test individual endpoints:
```bash
# Test active staff
curl -X POST http://localhost:8003/api/admin/staffs/active_staff \
  -H "Content-Type: application/json" \
  -d "{}"

# Test with authentication
curl -X POST http://localhost:8003/api/admin/staffs/active_staff \
  -H "Content-Type: application/json" \
  -H "Cookie: access_token=YOUR_TOKEN" \
  -d "{}"
```

## 📁 Project Structure

```
reports-fastapi/
├── app/
│   ├── database/connection.py    # Database connection
│   ├── models/auth.py           # Authentication models
│   ├── routers/
│   │   ├── auth.py             # Login/logout
│   │   └── all_reports.py      # All report APIs
│   ├── templates/              # HTML templates
│   └── main.py                 # FastAPI app
├── .env                        # Configuration
├── test_db.py                  # Database test
└── start.bat                   # Startup script
```

## 🔄 Development

1. **Add new report:**
   - Add endpoint in `app/routers/all_reports.py`
   - Create template in `app/templates/reports/`
   - Add link in dashboard

2. **Debug mode:**
   ```bash
   uvicorn app.main:app --reload --port 8003 --log-level debug
   ```

3. **View logs:**
   - Check console output
   - Enable logging in code:
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

## 🚀 Production Deployment

1. **Use production WSGI server:**
   ```bash
   pip install gunicorn
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

2. **Environment variables:**
   - Set strong SECRET_KEY
   - Use production database
   - Enable HTTPS

3. **Security:**
   - Use read-only database user
   - Enable CORS if needed
   - Set secure cookie flags

## 📞 Support

If you encounter issues:

1. Run `python test_db.py` to verify database connection
2. Check browser console for JavaScript errors
3. Verify all dependencies are installed
4. Ensure database tables exist and are accessible

## 🔗 API Endpoints

- `POST /api/admin/staffs/active_staff` - Active staff data
- `POST /api/admin/staffs/missing_credentials` - Missing credentials
- `POST /api/admin/patients/expired_auth` - Expired authorizations
- `POST /api/admin/appointments/scheduled_not_rendered` - Not rendered appointments
- `POST /api/admin/reports/schedule_billable` - Billable schedule

All endpoints require authentication via session cookies.