from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import JSONResponse
from jose import JWTError, jwt
from decouple import config
from datetime import datetime, timedelta
from ..database.connection import db
from typing import Optional

router = APIRouter()

SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config('ALGORITHM')

def get_current_user(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_admin_info(user_id: int):
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT is_up_admin, up_admin_id FROM admins WHERE id = %s", (user_id,))
    user_info = cursor.fetchone()
    admin_id = user_id if user_info['is_up_admin'] == 1 else user_info['up_admin_id']
    return admin_id

def get_client_name_query(admin_id: int):
    if admin_id == 35:
        return 'c.client_full_name'
    else:
        return """(CASE
                    WHEN (c.client_last_name IS NOT NULL) AND (c.client_first_name IS NOT NULL)
                    THEN CONCAT(c.client_last_name,', ',c.client_first_name)
                    ELSE c.client_full_name
                  END)"""

def get_staff_name_query(admin_id: int):
    if admin_id == 35:
        return 'e.full_name'
    else:
        return """(CASE
                    WHEN (e.last_name IS NOT NULL) AND (e.first_name IS NOT NULL)
                    THEN CONCAT(e.last_name,', ',e.first_name)
                    ELSE e.full_name
                  END)"""

# KPI Reports
@router.post("/api/admin/reports/kpi_by_insurance")
async def get_kpi_by_insurance(request: Request, 
                             start_date: Optional[str] = Form(None),
                             end_date: Optional[str] = Form(None),
                             user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    query = """
        SELECT ar.payor_id, p.payor_name,
        CAST(SUM(CAST(COALESCE(ar.billed_amount,0) AS FLOAT)) AS DECIMAL(10, 2)) as t_amount,
        CAST(SUM(CAST(COALESCE(ar.contracted_amount,0) AS FLOAT)) AS DECIMAL(10, 2)) as t_ct_amount,
        CAST(SUM(CAST(COALESCE(ar.payment,0) AS FLOAT)) AS DECIMAL(10, 2)) as t_payment,
        CAST(SUM(CAST(COALESCE(ar.adjustment,0) AS FLOAT)) AS DECIMAL(10, 2)) as t_adjustment,
        CAST(SUM(CAST(COALESCE(ar.balance,0) AS FLOAT)) AS DECIMAL(10, 2)) as t_balance,
        CAST(SUM(CAST(COALESCE(ar.ins_paid,0) AS FLOAT)) AS DECIMAL(10, 2)) as t_insurance
        FROM ar_reports as ar
        INNER JOIN payor_facilities as p ON p.payor_id = ar.payor_id AND p.admin_id = %s
        WHERE ar.admin_id = %s AND ar.dos BETWEEN %s AND %s AND ar.billed_amount > 0
        GROUP BY ar.payor_id, p.payor_name
        ORDER BY p.payor_name ASC
    """
    
    cursor.execute(query, (admin_id, admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

@router.post("/api/admin/reports/kpi_by_patient")
async def get_kpi_by_patient(request: Request, 
                           start_date: Optional[str] = Form(None),
                           end_date: Optional[str] = Form(None),
                           user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT ar.client_id, {client_name_query} as client_name,
        CAST(SUM(CAST(COALESCE(ar.billed_amount,0) AS FLOAT)) AS DECIMAL(10, 2)) as total_billed,
        CAST(SUM(CAST(COALESCE(ar.payment,0) AS FLOAT)) AS DECIMAL(10, 2)) as total_payment,
        CAST(SUM(CAST(COALESCE(ar.balance,0) AS FLOAT)) AS DECIMAL(10, 2)) as total_balance,
        COUNT(*) as total_sessions
        FROM ar_reports as ar
        LEFT JOIN clients as c ON c.id = ar.client_id
        WHERE ar.admin_id = %s AND ar.dos BETWEEN %s AND %s
        GROUP BY ar.client_id, c.client_full_name, c.client_first_name, c.client_last_name
        ORDER BY total_billed DESC
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# ABA Hour Reports
@router.post("/api/admin/reports/aba_hour_client")
async def get_aba_hour_client(request: Request, 
                            start_date: Optional[str] = Form(None),
                            end_date: Optional[str] = Form(None),
                            user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    
    # Get distinct clients with ABA services
    query = f"""
        SELECT DISTINCT a.client_id, {client_name_query} as client_full_name 
        FROM appoinments a 
        LEFT JOIN clients c ON c.id = a.client_id
        WHERE a.admin_id = %s AND a.schedule_date BETWEEN %s AND %s 
        AND EXISTS (SELECT * FROM app_services WHERE a.id = app_services.session_id 
                   AND cpt_code IN ('97153', '97155')) 
        AND a.status != 'deleted'
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    clients = cursor.fetchall()
    
    row_result = []
    for client in clients:
        client_id = client['client_id']
        
        # Direct hours worked
        cursor.execute("""
            SELECT SUM(COALESCE(time_duration,0)) as client_hours 
            FROM appoinments 
            WHERE client_id = %s 
            AND EXISTS (SELECT * FROM app_services WHERE appoinments.id = app_services.session_id 
                       AND cpt_code = '97153') 
            AND schedule_date BETWEEN %s AND %s 
            AND status = 'Rendered' AND status != 'deleted'
        """, (client_id, start_date, end_date))
        
        worked_direct_result = cursor.fetchone()
        worked_direct = (worked_direct_result['client_hours'] / 60) if worked_direct_result['client_hours'] else 0
        
        # Direct hours scheduled
        cursor.execute("""
            SELECT SUM(COALESCE(time_duration,0)) as client_hours 
            FROM appoinments 
            WHERE client_id = %s 
            AND EXISTS (SELECT * FROM app_services WHERE appoinments.id = app_services.session_id 
                       AND cpt_code = '97153') 
            AND schedule_date BETWEEN %s AND %s 
            AND status IN ('Scheduled','Rendered') AND status != 'deleted'
        """, (client_id, start_date, end_date))
        
        scheduled_direct_result = cursor.fetchone()
        scheduled_direct = (scheduled_direct_result['client_hours'] / 60) if scheduled_direct_result['client_hours'] else 0
        
        # Supervision hours worked
        cursor.execute("""
            SELECT SUM(COALESCE(time_duration,0)) as client_hours 
            FROM appoinments a 
            INNER JOIN app_services aps ON aps.session_id = a.id AND aps.cpt_code = '97155'
            WHERE a.client_id = %s AND a.schedule_date BETWEEN %s AND %s 
            AND a.status = 'Rendered'
        """, (client_id, start_date, end_date))
        
        worked_supervision_result = cursor.fetchone()
        worked_supervision = (worked_supervision_result['client_hours'] / 60) if worked_supervision_result['client_hours'] else 0
        
        # Supervision hours scheduled
        cursor.execute("""
            SELECT SUM(COALESCE(time_duration,0)) as client_hours 
            FROM appoinments a 
            INNER JOIN app_services aps ON aps.session_id = a.id AND aps.cpt_code = '97155'
            WHERE a.client_id = %s AND a.schedule_date BETWEEN %s AND %s 
            AND a.status IN ('Scheduled', 'Rendered')
        """, (client_id, start_date, end_date))
        
        scheduled_supervision_result = cursor.fetchone()
        scheduled_supervision = (scheduled_supervision_result['client_hours'] / 60) if scheduled_supervision_result['client_hours'] else 0
        
        # Calculate percentages
        worked_percentage = (worked_supervision / worked_direct * 100) if worked_direct > 0 else 0
        scheduled_percentage = (scheduled_supervision / scheduled_direct * 100) if scheduled_direct > 0 else 0
        
        row_result.append({
            'client_full_name': client['client_full_name'],
            'worked_direct': f"{worked_direct:.2f}",
            'scheduled_direct': f"{scheduled_direct:.2f}",
            'worked_supervision': f"{worked_supervision:.2f}",
            'scheduled_supervision': f"{scheduled_supervision:.2f}",
            'worked_percentage': f"{worked_percentage:.1f}",
            'scheduled_percentage': f"{scheduled_percentage:.1f}"
        })
    
    return JSONResponse(content=row_result)

# Session Missing Signature
@router.post("/api/admin/appointments/session_missing_signature")
async def get_session_missing_signature(request: Request, 
                                      start_date: Optional[str] = Form(None),
                                      end_date: Optional[str] = Form(None),
                                      user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    staff_name_query = get_staff_name_query(admin_id)
    
    today_date = datetime.now().strftime('%Y-%m-%d')
    
    query = f"""
        SELECT {client_name_query} as client_full_name, 
        {staff_name_query} as full_name, 
        a.id, a.provider_id, a.client_id, a.from_time, a.to_time, a.time_duration,
        CONCAT(TO_CHAR(CAST(a.from_time AS TIME), 'HH12:MI AM'),' - ',
               TO_CHAR(CAST(a.to_time AS TIME), 'HH12:MI AM')) as from_to_time,
        CASE WHEN (cas.signature IS NULL) THEN 'Not Available' ELSE 'Available' END as client_signature,
        CASE WHEN (pas.signature IS NULL) THEN 'Not Available' ELSE 'Available' END as provider_signature,
        CASE WHEN (sas.signature IS NULL) THEN 'Not Available' ELSE 'Available' END as supervisor_signature,
        TO_CHAR(CAST(a.schedule_date AS DATE), 'MM/DD/YYYY') as dos
        FROM appoinments as a 
        INNER JOIN employees as e ON e.id = a.provider_id 
        LEFT JOIN api_session_files as asf ON asf.session_id = a.id 
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN appoinment_signatures as cas ON cas.session_id = a.id AND cas.user_type = 1
        LEFT JOIN appoinment_signatures as pas ON pas.session_id = a.id AND pas.user_type = 2
        LEFT JOIN appoinment_signatures as sas ON sas.session_id = a.id AND sas.user_type = 3
        WHERE a.schedule_date BETWEEN %s AND %s AND a.admin_id = %s 
        AND (NOT EXISTS (SELECT * FROM appoinment_signatures WHERE a.id = appoinment_signatures.session_id AND user_type = 1) 
             OR NOT EXISTS (SELECT * FROM appoinment_signatures WHERE a.id = appoinment_signatures.session_id AND user_type = 2) 
             OR NOT EXISTS (SELECT * FROM appoinment_signatures WHERE a.id = appoinment_signatures.session_id AND user_type = 3)) 
        AND a.status = 'Rendered' AND asf.id IS NULL AND a.schedule_date < %s 
        AND a.billable = 1 AND a.status != 'deleted' 
        ORDER BY e.full_name ASC, a.schedule_date ASC
    """
    
    cursor.execute(query, (start_date, end_date, admin_id, today_date))
    results = cursor.fetchall()
    
    row_result = []
    for row in results:
        hours = (row['time_duration'] / 60) if row['time_duration'] else 0
        service = f"Service ({hours:.2f} Hrs)"
        
        row_result.append({
            'client_full_name': row['client_full_name'],
            'dos': row['dos'],
            'from_to_time': row['from_to_time'],
            'full_name': row['full_name'],
            'service': service,
            'client_signature': row['client_signature'],
            'provider_signature': row['provider_signature'],
            'supervisor_signature': row['supervisor_signature']
        })
    
    return JSONResponse(content=row_result)

# AR Ledger with Balance
@router.post("/api/admin/reports/ar_ledger_with_balance")
async def get_ar_ledger_with_balance(request: Request, 
                                   start_date: Optional[str] = Form(None),
                                   end_date: Optional[str] = Form(None),
                                   user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT ar.id, {client_name_query} as client_name, 
        p.payor_name, ar.cpt_code, ar.service_name,
        TO_CHAR(CAST(ar.dos AS DATE), 'MM/DD/YYYY') as dos,
        ar.billed_amount, ar.contracted_amount, ar.payment, 
        ar.adjustment, ar.balance, ar.ins_balance, ar.pat_balance
        FROM ar_reports as ar
        LEFT JOIN clients as c ON c.id = ar.client_id
        LEFT JOIN payor_facilities as p ON p.payor_id = ar.payor_id AND p.admin_id = %s
        WHERE ar.admin_id = %s AND ar.dos BETWEEN %s AND %s 
        AND ar.balance > 0
        ORDER BY ar.dos DESC
    """
    
    cursor.execute(query, (admin_id, admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Additional Patient Reports
@router.post("/api/admin/patients/auth_placeholder")
async def get_auth_placeholder(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT ca.id, ca.client_id, {client_name_query} as client_fullname,
        ap.payor_name, ca.authorization_number,
        TO_CHAR(CAST(ca.start_date AS DATE), 'MM/DD/YYYY') as start_date,
        TO_CHAR(CAST(ca.end_date AS DATE), 'MM/DD/YYYY') as end_date,
        'Placeholder' as auth_type
        FROM client_authorizations as ca
        LEFT JOIN clients as c ON c.id = ca.client_id
        LEFT JOIN all_payors as ap ON ap.id = ca.payor_id
        WHERE ca.admin_id = %s AND ca.is_placeholder = 1 
        AND ca.is_valid = 1 AND ca.is_deleted IS NULL
        ORDER BY ca.end_date DESC
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

@router.post("/api/admin/patients/non_payor_tag")
async def get_non_payor_tag(request: Request, user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT c.id as client_id, {client_name_query} as client_fullname,
        c.client_first_name, c.client_last_name, 
        'Patient/Guarantor Pay' as payment_type
        FROM clients as c
        WHERE c.admin_id = %s AND c.is_active_client = 1
        AND c.primary_payor_id IN (SELECT id FROM all_payors WHERE payor_name LIKE '%Patient%' OR payor_name LIKE '%Self%')
    """
    
    cursor.execute(query, (admin_id,))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])

# Payroll Reports
@router.post("/api/admin/reports/service_payroll_detail")
async def get_service_payroll_detail(request: Request, 
                                   start_date: Optional[str] = Form(None),
                                   end_date: Optional[str] = Form(None),
                                   user_id: int = Depends(get_current_user)):
    admin_id = get_admin_info(user_id)
    conn = db.get_connection(admin_id)
    cursor = conn.cursor()
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    staff_name_query = get_staff_name_query(admin_id)
    client_name_query = get_client_name_query(admin_id)
    
    query = f"""
        SELECT {staff_name_query} as provider_name,
        {client_name_query} as client_name,
        s.service_name, a.cpt_code,
        TO_CHAR(CAST(a.schedule_date AS DATE), 'MM/DD/YYYY') as service_date,
        a.time_duration / 60.0 as hours,
        er.hourly_rate, 
        (a.time_duration / 60.0) * er.hourly_rate as total_pay
        FROM appoinments as a
        LEFT JOIN employees as e ON e.id = a.provider_id
        LEFT JOIN clients as c ON c.id = a.client_id
        LEFT JOIN app_services as s ON s.session_id = a.id
        LEFT JOIN employee_rates as er ON er.employee_id = a.provider_id AND er.service_id = s.id
        WHERE a.admin_id = %s AND a.schedule_date BETWEEN %s AND %s
        AND a.status = 'Rendered'
        ORDER BY e.last_name, a.schedule_date
    """
    
    cursor.execute(query, (admin_id, start_date, end_date))
    results = cursor.fetchall()
    return JSONResponse(content=[dict(row) for row in results])